// French translations for center schema
export const centerTranslations = {
  // Document info
  document: {
    name: "center",
    title: "Centre Autocorner",
  },

  // Field translations
  fields: {
    name: "Nom du centre",
    slug: "Identifiant URL",
    centerType: "Type de centre",
    autoscoutSellerId: "ID Vendeur AutoScout24",
    shortDescription: "Description courte",
    image: "Image principale",
    altText: "Texte alternatif",
    caption: "Légende",
    pageBuilder: "Constructeur de page",
    address: "<PERSON>ress<PERSON>",
    contact: "Informations de contact",
    openingHours: "Heures d'ouverture",
    brandConfig: "Configuration de marque",
    certifications: "Certifications",
    specialties: "Spécialités",
    team: "Équipe",
    serviceAreas: "Zones de service",
    apiSettings: "Paramètres API",
    searchDefaults: "Recherche par défaut",
    displaySettings: "Paramètres d'affichage",
    seo: "SEO",

    // Center Type Options
    centerType_audi: "Audi",
    centerType_skoda: "Skoda",
    centerType_occasions: "Occasions",
    centerType_multibrands: "Multi-marques",

    // Address fields
    street: "Adresse",
    postalCode: "Code postal",
    city: "Ville",
    region: "Région/Canton",
    country: "Pays",
    coordinates: "Coordonnées GPS",

    // Contact fields
    phone: "Téléphone",
    email: "Adresse email",
    website: "Site web",
    whatsapp: "WhatsApp",

    // Opening hours
    day: "Jour",
    hours: "Heures",

    // Days of the week
    monday: "Lundi",
    tuesday: "Mardi",
    wednesday: "Mercredi",
    thursday: "Jeudi",
    friday: "Vendredi",
    saturday: "Samedi",
    sunday: "Dimanche",

    // Brand config
    primaryColor: "Couleur principale",
    secondaryColor: "Couleur secondaire",
    logo: "Logo",

    // Certifications
    certificationName: "Nom de la certification",
    certificationLogo: "Logo/Badge",
    certificationDescription: "Description",

    // Specialties options
    specialty_newVehicles: "Véhicules neufs",
    specialty_usedVehicles: "Véhicules d'occasion",
    specialty_electricVehicles: "Véhicules électriques",
    specialty_sportVehicles: "Véhicules sportifs",
    specialty_luxuryVehicles: "Véhicules de luxe",
    specialty_familyVehicles: "Véhicules familiaux",
    specialty_professionalLeasing: "Leasing professionnel",
    specialty_afterSalesService: "Service après-vente",
    specialty_bodyShop: "Carrosserie",
    specialty_partsAccessories: "Pièces et accessoires",
    specialty_vehicleTradeIn: "Reprise de véhicules",
    specialty_customFinancing: "Financement sur mesure",

    // Team fields
    memberName: "Nom complet",
    role: "Fonction",
    photo: "Photo",
    directPhone: "Téléphone direct",
    directEmail: "Email direct",
    specialization: "Spécialisation",

    // Team roles
    role_centerDirector: "Directeur de centre",
    role_salesAdvisor: "Conseiller de vente",
    role_serviceAdvisor: "Conseiller service",
    role_workshopManager: "Chef d'atelier",
    role_financeAdvisor: "Conseiller financement",
    role_serviceReception: "Réception service",

    // API Settings
    enableAutoSync: "Activer la synchronisation automatique",
    syncFrequency: "Fréquence de synchronisation",

    // Sync frequency options
    syncFreq_15min: "Toutes les 15 minutes",
    syncFreq_1hour: "Toutes les heures",
    syncFreq_6hours: "Toutes les 6 heures",
    syncFreq_daily: "Quotidiennement",

    // Search defaults
    defaultSort: "Tri par défaut",
    itemsPerPage: "Éléments par page",

    // Sort options
    sort_priceAsc: "Prix : croissant",
    sort_priceDesc: "Prix : décroissant",
    sort_yearDesc: "Année : plus récent d'abord",
    sort_mileageAsc: "Kilométrage : plus faible d'abord",
    sort_relevance: "Pertinence",

    // Display settings
    showPrices: "Afficher les prix",
    showFinancing: "Afficher les options de financement",
    featuredBadge: "Texte du badge véhicule vedette",
  },

  // Validation messages
  validation: {
    nameRequired: "Le nom du centre est requis",
    slugRequired: "L'identifiant URL est requis",
    centerTypeRequired: "Le type de centre est requis",
    autoscoutSellerIdRequired: "L'ID AutoScout24 est requis",
    streetRequired: "L'adresse est requise",
    postalCodeRequired: "Le code postal est requis",
    cityRequired: "La ville est requise",
    phoneRequired: "Le numéro de téléphone est requis",
    emailRequired: "L'adresse email est requise",
    emailValid: "Veuillez saisir une adresse email valide",
    memberNameRequired: "Le nom du membre de l'équipe est requis",
    roleRequired: "La fonction est requise",
    itemsPerPageRange: "Le nombre d'éléments par page doit être entre 6 et 50",
  },

  // Field descriptions/help text
  descriptions: {
    name: 'Nom officiel du centre (ex: "Audi Lutry", "Skoda Romanel")',
    slug: "Identifiant URL unique pour ce centre (généré automatiquement)",
    centerType: "Spécialisation principale de ce centre",
    autoscoutSellerId: "Identifiant vendeur AutoScout24 pour l'intégration API",
    shortDescription:
      "Description brève du centre pour les listes et résultats de recherche",
    image: "Image principale du centre (showroom, façade, etc.)",
    pageBuilder:
      "Créez des pages riches pour ce centre avec des blocs flexibles",
    address: "Informations de localisation physique",
    contact: "Coordonnées pour les clients",
    openingHours: "Heures d'ouverture pour chaque jour de la semaine",
    brandConfig: "Configuration visuelle spécifique à la marque",
    certifications: "Certifications qualité et badges officiels",
    specialties: "Domaines d'expertise et services spécialisés",
    team: "Personnel clé et conseillers de vente",
    serviceAreas: "Régions géographiques desservies par ce centre",
    apiSettings: "Configuration technique pour l'intégration AutoScout24",
    searchDefaults:
      "Filtres de recherche de véhicules par défaut pour ce centre",
    displaySettings: "Préférences de personnalisation de l'interface",
    seo: "Paramètres d'optimisation pour les moteurs de recherche",
    coordinates:
      "Position GPS exacte pour les cartes et services de localisation",
    whatsapp: "Numéro WhatsApp pour contact client rapide (optionnel)",
    specialization:
      "Domaines d'expertise (marques, services, types de véhicules)",
    enableAutoSync:
      "Synchroniser automatiquement les données de véhicules depuis AutoScout24",
    syncFrequency: "Fréquence de mise à jour de l'inventaire de véhicules",
    hours: "ex: 08:00-12:00 / 13:30-18:00 ou Fermé",
  },
} as const;
