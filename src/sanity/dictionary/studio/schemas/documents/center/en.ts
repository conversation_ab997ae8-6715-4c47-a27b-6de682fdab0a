// English translations for center schema
export const centerTranslations = {
  // Document info
  document: {
    name: "center",
    title: "Autocorner Center",
  },

  // Field translations
  fields: {
    name: "Center Name",
    slug: "URL Slug",
    centerType: "Center Type",
    autoscoutSellerId: "AutoScout24 Seller ID",
    shortDescription: "Short Description",
    image: "Main Image",
    altText: "Alt Text",
    caption: "Caption",
    pageBuilder: "Page Builder",
    address: "Address",
    contact: "Contact Information",
    openingHours: "Opening Hours",
    brandConfig: "Brand Configuration",
    certifications: "Certifications",
    specialties: "Specialties",
    team: "Team Members",
    serviceAreas: "Service Areas",
    apiSettings: "API Settings",
    searchDefaults: "Search Defaults",
    displaySettings: "Display Settings",
    seo: "SEO",

    // Center Type Options
    centerType_audi: "Audi",
    centerType_skoda: "Skoda",
    centerType_occasions: "Premium Occasions",
    centerType_multibrands: "Multi-brands",

    // Address fields
    street: "Street Address",
    postalCode: "Postal Code",
    city: "City",
    region: "Region/Canton",
    country: "Country",
    coordinates: "GPS Coordinates",

    // Contact fields
    phone: "Phone Number",
    email: "Email Address",
    website: "Website",
    whatsapp: "WhatsApp",

    // Opening hours
    day: "Day",
    hours: "Hours",

    // Days of the week
    monday: "Monday",
    tuesday: "Tuesday",
    wednesday: "Wednesday",
    thursday: "Thursday",
    friday: "Friday",
    saturday: "Saturday",
    sunday: "Sunday",

    // Brand config
    primaryColor: "Primary Color",
    secondaryColor: "Secondary Color",
    logo: "Logo",

    // Certifications
    certificationName: "Certification Name",
    certificationLogo: "Logo/Badge",
    certificationDescription: "Description",

    // Specialties options
    specialty_newVehicles: "New Vehicles",
    specialty_usedVehicles: "Used Vehicles",
    specialty_electricVehicles: "Electric Vehicles",
    specialty_sportVehicles: "Sport Vehicles",
    specialty_luxuryVehicles: "Luxury Vehicles",
    specialty_familyVehicles: "Family Vehicles",
    specialty_professionalLeasing: "Professional Leasing",
    specialty_afterSalesService: "After-Sales Service",
    specialty_bodyShop: "Body Shop",
    specialty_partsAccessories: "Parts & Accessories",
    specialty_vehicleTradeIn: "Vehicle Trade-In",
    specialty_customFinancing: "Custom Financing",

    // Team fields
    memberName: "Full Name",
    role: "Role",
    photo: "Photo",
    directPhone: "Direct Phone",
    directEmail: "Direct Email",
    specialization: "Specialization",

    // Team roles
    role_centerDirector: "Center Director",
    role_salesAdvisor: "Sales Advisor",
    role_serviceAdvisor: "Service Advisor",
    role_workshopManager: "Workshop Manager",
    role_financeAdvisor: "Finance Advisor",
    role_serviceReception: "Service Reception",

    // API Settings
    enableAutoSync: "Enable Auto Sync",
    syncFrequency: "Sync Frequency",

    // Sync frequency options
    syncFreq_15min: "Every 15 minutes",
    syncFreq_1hour: "Every hour",
    syncFreq_6hours: "Every 6 hours",
    syncFreq_daily: "Daily",

    // Search defaults
    defaultSort: "Default Sort Order",
    itemsPerPage: "Items Per Page",

    // Sort options
    sort_priceAsc: "Price: Low to High",
    sort_priceDesc: "Price: High to Low",
    sort_yearDesc: "Year: Newest First",
    sort_mileageAsc: "Mileage: Lowest First",
    sort_relevance: "Relevance",

    // Display settings
    showPrices: "Show Prices",
    showFinancing: "Show Financing Options",
    featuredBadge: "Featured Vehicle Badge Text",
  },

  // Validation messages
  validation: {
    nameRequired: "Center name is required",
    slugRequired: "URL slug is required",
    centerTypeRequired: "Center type is required",
    autoscoutSellerIdRequired: "AutoScout24 Seller ID is required",
    streetRequired: "Street address is required",
    postalCodeRequired: "Postal code is required",
    cityRequired: "City is required",
    phoneRequired: "Phone number is required",
    emailRequired: "Email address is required",
    emailValid: "Please enter a valid email address",
    memberNameRequired: "Team member name is required",
    roleRequired: "Role is required",
    itemsPerPageRange: "Items per page must be between 6 and 50",
  },

  // Field descriptions/help text
  descriptions: {
    name: 'Official name of the center (e.g., "Audi Lutry", "Skoda Romanel")',
    slug: "URL-friendly identifier for this center (auto-generated from name)",
    centerType: "Main specialization of this center",
    autoscoutSellerId:
      "AutoScout24 seller identifier for vehicle API integration",
    shortDescription:
      "Brief description of the center for listings and search results",
    image: "Main presentation image of the center (showroom, facade, etc.)",
    pageBuilder:
      "Build rich content pages for this center using flexible blocks",
    address: "Physical location information",
    contact: "Contact details for customers",
    openingHours: "Business hours for each day of the week",
    brandConfig: "Brand-specific visual configuration",
    certifications: "Quality certifications and official badges",
    specialties: "Areas of expertise and specialized services",
    team: "Key personnel and sales advisors",
    serviceAreas: "Geographic regions served by this center",
    apiSettings: "Technical configuration for AutoScout24 integration",
    searchDefaults: "Default vehicle search filters for this center",
    displaySettings: "UI customization preferences",
    seo: "Search engine optimization settings for this center",
    coordinates: "Exact GPS position for maps and location services",
    whatsapp: "WhatsApp number for quick customer contact (optional)",
    specialization: "Areas of expertise (brands, services, vehicle types)",
    enableAutoSync: "Automatically sync vehicle data from AutoScout24",
    syncFrequency: "How often to update vehicle inventory",
    hours: "e.g., 08:00-12:00 / 13:30-18:00 or Closed",
  },
} as const;
