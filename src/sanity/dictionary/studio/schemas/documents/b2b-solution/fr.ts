// French translations for b2b-solution schema
export const b2bSolutionTranslations = {
  // Document info
  document: {
    name: "b2bSolution",
    title: "Solution B2B",
  },

  // Field translations
  fields: {
    name: "Nom de la solution",
    slug: "Identifiant URL",
    brand: "Marque",
    solutionType: "Type de solution",
    shortDescription: "Description courte",
    description: "Description détaillée",
    heroImage: "Image principale",
    altText: "Texte alternatif",

    // Business Targeting
    targetSegments: "Segments d'entreprise cibles",
    businessSizes: "Tailles d'entreprise cibles",
    industries: "Secteurs d'activité cibles",
    geographicScope: "Portée géographique",
    targetDescription: "Description du public cible",

    // Service Portfolio
    serviceCategories: "Catégories de services",
    categoryTitle: "Titre de la catégorie",
    categoryDescription: "Description de la catégorie",
    categoryIcon: "Icône de la catégorie",
    services: "Services",
    featuredServices: "Services mis en avant",
    serviceHighlights: "Points forts des services",
    highlightTitle: "Titre du point fort",
    highlightDescription: "Description du point fort",
    highlightIcon: "Icône du point fort",

    // Fleet Integration
    fleetSolutions: "Solutions de flotte",
    fleetIntegrationLevel: "Niveau d'intégration flotte",
    supportedFleetTypes: "Types de flottes supportés",
    fleetBenefits: "Avantages flotte",

    // Business Benefits
    businessBenefits: "Avantages business",
    benefitTitle: "Titre de l'avantage",
    benefitMetric: "Métrique de l'avantage",
    benefitDescription: "Description de l'avantage",
    benefitIcon: "Icône de l'avantage",
    valueProposition: "Proposition de valeur",
    competitiveAdvantages: "Avantages concurrentiels",
    advantageTitle: "Titre de l'avantage",
    advantageDescription: "Description de l'avantage",

    // Key Contacts
    keyContacts: "Contacts clés",
    contactName: "Nom du contact",
    contactRole: "Rôle du contact",
    contactEmail: "Email du contact",
    contactPhone: "Téléphone du contact",
    contactPhoto: "Photo du contact",
    contactCenter: "Centre du contact",
    contactSpecialties: "Spécialités du contact",
    contactBio: "Biographie du contact",

    // Customer Cases
    featuredProjects: "Cas clients mis en avant",
    customerTestimonials: "Témoignages clients",
    successStories: "Histoires de succès",
    caseStudyHighlights: "Points forts des cas d'étude",

    // B2B Content
    featuredContent: "Contenu B2B mis en avant",
    resourceLibrary: "Bibliothèque de ressources",
    whitepapers: "Livres blancs",
    caseStudies: "Études de cas",
    guides: "Guides d'implémentation",

    // Solution Configuration
    isActive: "Solution active",
    isPremium: "Solution premium",
    requiresConsultation: "Nécessite une consultation",
    leadMagnet: "Aimant à prospects",
    consultationRequired: "Consultation obligatoire",
    customQuoteRequired: "Devis personnalisé requis",

    // Page Builder & SEO
    pageBuilder: "Constructeur de page",
    seo: "SEO",

    // Solution type options
    solutionType_comprehensive: "Solutions complètes",
    solutionType_fleet: "Gestion de flotte",
    solutionType_leasing: "Leasing d'entreprise",
    solutionType_service: "Solutions de service",
    solutionType_consultation: "Consultation business",

    // Geographic scope options
    geographicScope_local: "Local",
    geographicScope_regional: "Régional",
    geographicScope_national: "National",
    geographicScope_international: "International",

    // Fleet integration level options
    fleetIntegrationLevel_basic: "Intégration de base",
    fleetIntegrationLevel_standard: "Intégration standard",
    fleetIntegrationLevel_premium: "Intégration premium",
    fleetIntegrationLevel_enterprise: "Intégration entreprise",
  },

  // Validation messages
  validation: {
    nameRequired: "Le nom de la solution est requis",
    slugRequired: "L'identifiant URL est requis",
    brandRequired: "La sélection de marque est requise",
    solutionTypeRequired: "Le type de solution est requis",
    shortDescriptionRequired: "La description courte est requise",
    descriptionRequired: "La description détaillée est requise",
    targetSegmentsRequired: "Au moins un segment cible est requis",
    businessSizesRequired: "Au moins une taille d'entreprise est requise",
    categoriesRequired: "Au moins une catégorie de service est requise",
    categoryTitleRequired: "Le titre de la catégorie est requis",
    categoryDescriptionRequired: "La description de la catégorie est requise",
    benefitTitleRequired: "Le titre de l'avantage est requis",
    benefitDescriptionRequired: "La description de l'avantage est requise",
    contactNameRequired: "Le nom du contact est requis",
    contactRoleRequired: "Le rôle du contact est requis",
    contactEmailRequired: "L'email du contact est requis",
    contactEmailValid: "Veuillez entrer une adresse email valide",
    contactPhoneValid: "Veuillez entrer un numéro de téléphone valide",
    highlightTitleRequired: "Le titre du point fort est requis",
    advantageTitleRequired: "Le titre de l'avantage est requis",
  },

  // Field descriptions/help text
  descriptions: {
    name: 'Nom de la solution B2B (ex: "Solutions Pro Audi", "Solutions Flotte Skoda")',
    slug: "Identifiant URL pour cette solution (généré automatiquement depuis le nom)",
    brand: "Marque à laquelle appartient cette solution (Audi, Skoda, etc.)",
    solutionType: "Type de solution business proposée",
    shortDescription:
      "Description courte de la solution pour les listes et aperçus (max 160 caractères)",
    description:
      "Description complète de la solution pour les pages détaillées",
    heroImage:
      "Image principale de la solution pour les sections hero et affichages mis en avant",
    targetSegments:
      "Segments d'entreprise que cette solution cible (référence business-segments)",
    businessSizes:
      "Tailles d'entreprise pour lesquelles cette solution est conçue (référence business-sizes)",
    industries:
      "Industries que cette solution sert spécifiquement (référence business-industries)",
    geographicScope: "Portée géographique de cette solution",
    targetDescription: "Description détaillée du profil client idéal",
    serviceCategories:
      "Principales catégories de services offertes dans cette solution",
    categoryTitle: "Nom de la catégorie de service",
    categoryDescription: "Description de ce que cette catégorie inclut",
    categoryIcon: "Icône visuelle représentant cette catégorie de service",
    services:
      'Services spécifiques inclus dans cette catégorie (référence services avec serviceScope = "b2b")',
    featuredServices: "Services clés à mettre en avant pour cette solution",
    serviceHighlights:
      "Fonctionnalités et capacités clés des services à emphasiser",
    fleetSolutions: "Solutions de flotte connectées à cette solution B2B",
    fleetIntegrationLevel: "Niveau d'intégration de gestion de flotte offert",
    supportedFleetTypes: "Types de flottes que cette solution supporte",
    fleetBenefits: "Avantages spécifiques pour les clients flotte",
    businessBenefits: "Avantages business clés et propositions de valeur",
    benefitTitle: "Nom de l'avantage business",
    benefitMetric: 'Métrique quantifiable (ex: "35+", "90%", "€500K")',
    benefitDescription:
      "Explication de comment cet avantage impacte l'entreprise",
    benefitIcon: "Représentation visuelle de cet avantage",
    valueProposition: "Déclaration de proposition de valeur principale",
    competitiveAdvantages: "Avantages clés par rapport aux concurrents",
    keyContacts: "Personnel clé responsable de cette solution",
    contactName: "Nom complet de la personne contact",
    contactRole: "Titre de poste ou rôle dans l'organisation",
    contactEmail: "Adresse email directe pour les demandes business",
    contactPhone: "Numéro de téléphone direct pour les demandes business",
    contactPhoto: "Photo professionnelle de la personne contact",
    contactCenter: "Centre Autocorner où cette personne est basée",
    contactSpecialties: "Domaines d'expertise et de spécialisation",
    contactBio: "Parcours professionnel et expérience",
    featuredProjects:
      'Études de cas clients et histoires de succès (référence projects avec projectType = "b2b")',
    customerTestimonials: "Commentaires et témoignages clients",
    successStories: "Histoires de succès détaillées et résultats",
    featuredContent:
      'Contenu et ressources B2B mis en avant (référence posts avec contentScope = "b2b")',
    resourceLibrary: "Bibliothèque de ressources et matériaux B2B",
    isActive: "Si cette solution est actuellement active et disponible",
    isPremium: "Marquer comme solution premium avec fonctionnalités améliorées",
    requiresConsultation:
      "Si cette solution nécessite une consultation initiale",
    leadMagnet:
      "Utiliser cette solution comme outil de génération de prospects",
    consultationRequired: "La consultation est obligatoire avant de procéder",
    customQuoteRequired: "Devis personnalisé requis pour la tarification",
    pageBuilder: "Mise en page personnalisée et sections de contenu",
    seo: "Paramètres d'optimisation pour les moteurs de recherche des pages de solution",
  },
} as const;
