// English translations for b2b-solution schema
export const b2bSolutionTranslations = {
  // Document info
  document: {
    name: "b2bSolution",
    title: "B2B Solution",
  },

  // Field translations
  fields: {
    name: "Solution Name",
    slug: "URL Slug",
    brand: "Brand",
    solutionType: "Solution Type",
    shortDescription: "Short Description",
    description: "Detailed Description",
    heroImage: "Hero Image",
    altText: "Alt Text",

    // Business Targeting
    targetSegments: "Target Business Segments",
    businessSizes: "Target Business Sizes",
    industries: "Target Industries",
    geographicScope: "Geographic Scope",
    targetDescription: "Target Audience Description",

    // Service Portfolio
    serviceCategories: "Service Categories",
    categoryTitle: "Category Title",
    categoryDescription: "Category Description",
    categoryIcon: "Category Icon",
    services: "Services",
    featuredServices: "Featured Services",
    serviceHighlights: "Service Highlights",
    highlightTitle: "Highlight Title",
    highlightDescription: "Highlight Description",
    highlightIcon: "Highlight Icon",

    // Fleet Integration
    fleetSolutions: "Fleet Solutions",
    fleetIntegrationLevel: "Fleet Integration Level",
    supportedFleetTypes: "Supported Fleet Types",
    fleetBenefits: "Fleet Benefits",

    // Business Benefits
    businessBenefits: "Business Benefits",
    benefitTitle: "Benefit Title",
    benefitMetric: "Benefit Metric",
    benefitDescription: "Benefit Description",
    benefitIcon: "Benefit Icon",
    valueProposition: "Value Proposition",
    competitiveAdvantages: "Competitive Advantages",
    advantageTitle: "Advantage Title",
    advantageDescription: "Advantage Description",

    // Key Contacts
    keyContacts: "Key Contacts",
    contactName: "Contact Name",
    contactRole: "Contact Role",
    contactEmail: "Contact Email",
    contactPhone: "Contact Phone",
    contactPhoto: "Contact Photo",
    contactCenter: "Contact Center",
    contactSpecialties: "Contact Specialties",
    contactBio: "Contact Bio",

    // Customer Cases
    featuredProjects: "Featured Customer Cases",
    customerTestimonials: "Customer Testimonials",
    successStories: "Success Stories",
    caseStudyHighlights: "Case Study Highlights",

    // B2B Content
    featuredContent: "Featured B2B Content",
    resourceLibrary: "Resource Library",
    whitepapers: "Whitepapers",
    caseStudies: "Case Studies",
    guides: "Implementation Guides",

    // Solution Configuration
    isActive: "Active Solution",
    isPremium: "Premium Solution",
    requiresConsultation: "Requires Consultation",
    leadMagnet: "Lead Magnet",
    consultationRequired: "Consultation Required",
    customQuoteRequired: "Custom Quote Required",

    // Page Builder & SEO
    pageBuilder: "Page Builder",
    seo: "SEO",

    // Solution type options
    solutionType_comprehensive: "Comprehensive Solutions",
    solutionType_fleet: "Fleet Management",
    solutionType_leasing: "Corporate Leasing",
    solutionType_service: "Service Solutions",
    solutionType_consultation: "Business Consultation",

    // Geographic scope options
    geographicScope_local: "Local",
    geographicScope_regional: "Regional",
    geographicScope_national: "National",
    geographicScope_international: "International",

    // Fleet integration level options
    fleetIntegrationLevel_basic: "Basic Integration",
    fleetIntegrationLevel_standard: "Standard Integration",
    fleetIntegrationLevel_premium: "Premium Integration",
    fleetIntegrationLevel_enterprise: "Enterprise Integration",
  },

  // Validation messages
  validation: {
    nameRequired: "Solution name is required",
    slugRequired: "URL slug is required",
    brandRequired: "Brand selection is required",
    solutionTypeRequired: "Solution type is required",
    shortDescriptionRequired: "Short description is required",
    descriptionRequired: "Detailed description is required",
    targetSegmentsRequired: "At least one target segment is required",
    businessSizesRequired: "At least one business size is required",
    categoriesRequired: "At least one service category is required",
    categoryTitleRequired: "Category title is required",
    categoryDescriptionRequired: "Category description is required",
    benefitTitleRequired: "Benefit title is required",
    benefitDescriptionRequired: "Benefit description is required",
    contactNameRequired: "Contact name is required",
    contactRoleRequired: "Contact role is required",
    contactEmailRequired: "Contact email is required",
    contactEmailValid: "Please enter a valid email address",
    contactPhoneValid: "Please enter a valid phone number",
    highlightTitleRequired: "Highlight title is required",
    advantageTitleRequired: "Advantage title is required",
  },

  // Field descriptions/help text
  descriptions: {
    name: 'Name of the B2B solution (e.g., "Solutions Pro Audi", "Fleet Solutions Skoda")',
    slug: "URL-friendly identifier for this solution (auto-generated from name)",
    brand: "Brand this solution belongs to (Audi, Skoda, etc.)",
    solutionType: "Type of business solution offered",
    shortDescription:
      "Brief solution description for listings and previews (max 160 characters)",
    description: "Comprehensive solution description for detailed pages",
    heroImage: "Main solution image for hero sections and featured displays",
    targetSegments:
      "Business segments this solution targets (references business-segments)",
    businessSizes:
      "Company sizes this solution is designed for (references business-sizes)",
    industries:
      "Industries this solution specifically serves (references business-industries)",
    geographicScope: "Geographic reach of this solution",
    targetDescription: "Detailed description of the ideal customer profile",
    serviceCategories: "Main service categories offered within this solution",
    categoryTitle: "Name of the service category",
    categoryDescription: "Description of what this category includes",
    categoryIcon: "Visual icon representing this service category",
    services:
      'Specific services included in this category (references services with serviceScope = "b2b")',
    featuredServices: "Key services to highlight for this solution",
    serviceHighlights: "Key service features and capabilities to emphasize",
    fleetSolutions: "Fleet solutions connected to this B2B solution",
    fleetIntegrationLevel: "Level of fleet management integration offered",
    supportedFleetTypes: "Types of fleets this solution supports",
    fleetBenefits: "Specific benefits for fleet customers",
    businessBenefits: "Key business benefits and value propositions",
    benefitTitle: "Name of the business benefit",
    benefitMetric: 'Quantifiable metric (e.g., "35+", "90%", "€500K")',
    benefitDescription: "Explanation of how this benefit impacts the business",
    benefitIcon: "Visual representation of this benefit",
    valueProposition: "Core value proposition statement",
    competitiveAdvantages: "Key advantages over competitors",
    keyContacts: "Key personnel responsible for this solution",
    contactName: "Full name of the contact person",
    contactRole: "Job title or role within the organization",
    contactEmail: "Direct email address for business inquiries",
    contactPhone: "Direct phone number for business inquiries",
    contactPhoto: "Professional photo of the contact person",
    contactCenter: "Autocorner center this contact is based at",
    contactSpecialties: "Areas of expertise and specialization",
    contactBio: "Professional background and experience",
    featuredProjects:
      'Customer case studies and success stories (references projects with projectType = "b2b")',
    customerTestimonials: "Customer feedback and testimonials",
    successStories: "Detailed success stories and outcomes",
    featuredContent:
      'Featured B2B content and resources (references posts with contentScope = "b2b")',
    resourceLibrary: "Library of B2B resources and materials",
    isActive: "Whether this solution is currently active and available",
    isPremium: "Mark as premium solution with enhanced features",
    requiresConsultation: "Whether this solution requires initial consultation",
    leadMagnet: "Use this solution as a lead generation tool",
    consultationRequired: "Consultation is mandatory before proceeding",
    customQuoteRequired: "Custom quotation required for pricing",
    pageBuilder: "Custom page layout and content sections",
    seo: "Search engine optimization settings for solution pages",
  },
} as const;
