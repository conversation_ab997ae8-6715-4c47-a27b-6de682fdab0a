"use client";

import { memo, Suspense } from "react";
import dynamic from "next/dynamic";
import { LucideProps } from "lucide-react";
import type { IconName } from "lucide-react/dynamic";

export interface DynamicIconProps extends LucideProps {
  name: IconName;
  fallback?: React.ComponentType<LucideProps>;
}

// Icon Loading Fallback
function IconFallback({ size = 24, ...props }: LucideProps) {
  return (
    <div
      //@ts-expect-error: SVG ref is not compatible with HTMLDivElement ref
      ref={props.ref}
      style={{
        width: size,
        height: size,
        backgroundColor: "#e2e2e2",
        borderRadius: "2px",
        display: "inline-block",
        flexShrink: 0,
      }}
      {...props}
    />
  );
}

// Dynamic Icon Component with Optimized Loading for Sanity Studio
export const DynamicIcon = memo<DynamicIconProps>(
  ({ name, fallback: Fallback = IconFallback, ...props }) => {
    try {
      // Check if we're in a browser environment
      if (typeof window === "undefined") {
        return <Fallback {...props} />;
      }

      // Check if the icon name is provided
      if (!name) {
        return <Fallback {...props} />;
      }

      const LucideIcon = dynamic(
        async () => {
          try {
            const dynamicIconImports = await import(
              "lucide-react/dynamicIconImports"
            );
            const iconImports = dynamicIconImports.default;

            if (name in iconImports) {
              const iconLoader = iconImports[name as keyof typeof iconImports];
              return await iconLoader();
            } else {
              console.warn(`Icon "${name}" not found in Lucide icons`);
              return Fallback;
            }
          } catch (error) {
            console.warn(`Failed to load icon: ${name}`, error);
            return Fallback;
          }
        },
        {
          ssr: false,
          loading: () => <Fallback {...props} />,
        },
      );

      return (
        <Suspense fallback={<Fallback {...props} />}>
          <LucideIcon {...props} />
        </Suspense>
      );
    } catch (error) {
      console.warn(`Failed to load icon: ${name}`, error);
      return <Fallback {...props} />;
    }
  },
);

DynamicIcon.displayName = "DynamicIcon";
