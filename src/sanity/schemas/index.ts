import type { SchemaTypeDefinition } from "sanity";

import center from "./documents/center";
import brand from "./documents/brand";
import vehicleModel from "./documents/vehicle-model";
import offer from "./documents/offer";
import b2bSolution from "./documents/b2b-solution";
import fleet from "./documents/fleet";
import generalSettings from "./singletons/general-settings";
import navigationSettings from "./singletons/navigation-settings";
import blogSettings from "./singletons/blog-settings";
import marketingSettings from "./singletons/marketing-settings";
import redirect from "./documents/redirect";
import page from "./documents/page";
import post from "./documents/post";
import postCategory from "./documents/post-category";
import author from "./documents/author";
import testimonial from "./documents/testimonial";
import service from "./documents/service";
import form from "./documents/form";
import project from "./documents/project";
import projectCategory from "./documents/project-category";
import servicesPage from "./singletons/pages/services-page";
import blogPage from "./singletons/pages/blog-page";
import projectsPage from "./singletons/pages/projects-page";
import b2bSolutionsPage from "./singletons/pages/b2b-solutions-page";
import fleetPage from "./singletons/pages/fleet-page";
import brandsPage from "./singletons/pages/brands-page";
import searchPage from "./singletons/pages/search-page";
import offersPage from "./singletons/pages/offers-page";
import centersPage from "./singletons/pages/centers-page";
import { pageBuilder } from "./page-builder/page-builder";
import headerBlock from "./page-builder/blocks/header-block";
import heroBlock from "./page-builder/blocks/hero-block";
import logoBlock from "./page-builder/blocks/logo-block";
import featuresMinimalBlock from "./page-builder/blocks/features-minimal-block";
import featureCardsBlock from "./page-builder/blocks/feature-cards-block";
import callToActionBlock from "./page-builder/blocks/call-to-action-block";
import testimonialBlock from "./page-builder/blocks/testimonial-block";
import portableTextBlock from "./page-builder/blocks/portable-text-block";
import freeformBlock from "./page-builder/blocks/freeform-block";
import servicesBlock from "./page-builder/blocks/services-block";
import formBlock from "./page-builder/blocks/form-block";
import mediaBlock from "./page-builder/blocks/media-block";
import gridLayoutBlock from "./page-builder/blocks/grid-layout-block";

// New Advanced Blocks
import contentGridsBlock from "./page-builder/blocks/content-grids-block";
import processTimelinesBlock from "./page-builder/blocks/process-timelines-block";
import statisticsBlock from "./page-builder/blocks/statistics-block";
import carouselBlock from "./page-builder/blocks/carousel-block";
import { vehicleListBlock } from "./page-builder/blocks/vehicle-list-block";
import { vehicleSearchToolbar } from "./page-builder/blocks/vehicle-search-toolbar";
import seoObject from "./objects/seo";
import addressObject from "./objects/address";
import contactObject from "./objects/contact";
import headingObject from "./objects/heading";
import richTextObject from "./objects/rich-text";
import spacerObject from "./objects/spacer";
import videoObject from "./objects/video";
import buttonObject from "./objects/button";
import singleImageObject from "./objects/single-image";
import callToActionObject from "./objects/call-to-action";
import { gridConfig } from "./objects/grid-config";
import { gridItemConfig } from "./objects/grid-item-config";
import sectionStyling from "./objects/section-styling";

// New Advanced Objects
import { contentItemObject } from "./objects/content-item";
import { filterConfigObject } from "./objects/filter-config";
import { filterItemObject } from "./objects/filter-item";
import { timelineStepObject } from "./objects/timeline-step";
import { timelineConfigObject } from "./objects/timeline-config";
import { animationConfigObject } from "./objects/animation-config";
import { carouselConfigObject } from "./objects/carousel-config";
// Headless Data Source System
import { dataSourceObject } from "./objects/data-source";
import muxVideoObject from "./objects/mux-video";
import b2bService from "./objects/b2b-service";
import b2bProject from "./objects/b2b-project";
import b2bPost from "./objects/b2b-post";
import b2bPostCategory from "./objects/b2b-post-category";
import b2bProjectCategory from "./objects/b2b-project-category";
// AutoScout
import { fuelTypeObject, fuelTypeArray } from "./objects/fuel-type";
import {
  conditionTypeObject,
  conditionTypeArray,
} from "./objects/condition-type";
// Icons
import { lucideIconObject } from "./objects/lucide-icon";

const coreSchema = [
  generalSettings,
  navigationSettings,
  marketingSettings,
  blogSettings,
  redirect,
  page,
  post,
  postCategory,
  author,
  testimonial,
  projectCategory,
  project,
  form,
  blogPage,
  service,
  servicesPage,
  projectsPage,
  b2bSolutionsPage,
  fleetPage,
  brandsPage,
  searchPage,
  offersPage,
  centersPage,
  center,
  brand,
  vehicleModel,
  offer,
  b2bSolution,
  fleet,
];

const pageBuilderSchema = [
  pageBuilder,
  heroBlock,
  headerBlock,
  featureCardsBlock,
  featuresMinimalBlock,
  freeformBlock,
  portableTextBlock,
  callToActionBlock,
  logoBlock,
  mediaBlock,
  testimonialBlock,
  servicesBlock,
  formBlock,
  gridLayoutBlock,
  // New Advanced Blocks
  contentGridsBlock,
  processTimelinesBlock,
  statisticsBlock,
  carouselBlock,
  vehicleListBlock,
  vehicleSearchToolbar,
];

const objectSchema = [
  seoObject,
  addressObject,
  contactObject,
  headingObject,
  richTextObject,
  buttonObject,
  singleImageObject,
  spacerObject,
  callToActionObject,
  videoObject,
  muxVideoObject,
  gridConfig,
  gridItemConfig,
  sectionStyling,
  // New Advanced Objects
  contentItemObject,
  filterConfigObject,
  filterItemObject,
  timelineStepObject,
  timelineConfigObject,
  animationConfigObject,
  carouselConfigObject,
  // Headless Data Source System
  dataSourceObject,
  // B2B Extensions
  b2bService,
  b2bProject,
  b2bPost,
  b2bPostCategory,
  b2bProjectCategory,
  // AutoScout
  fuelTypeObject,
  fuelTypeArray,
  conditionTypeObject,
  conditionTypeArray,
  //bodyTypeObject,
  //transmissionTypeObject,
  // Icons
  lucideIconObject,
];

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [...coreSchema, ...pageBuilderSchema, ...objectSchema],
};
