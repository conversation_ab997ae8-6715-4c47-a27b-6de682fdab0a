import { Clapperboard } from "lucide-react";
import { defineField, defineType } from "sanity";
import { callToActionObjectDict } from "../../dictionary/studio/schemas/objects/call-to-action";

const { fields } = callToActionObjectDict.en;

export default defineType({
  name: "callToActionObject",
  title: fields.title,
  type: "object",
  fields: [
    defineField({
      name: "callToActionTitle",
      type: "string",
      title: fields.callToActionTitle,
    }),
    defineField({
      name: "callToActionParagraph",
      type: "text",
      title: fields.callToActionParagraph,
      rows: 4,
    }),
    defineField({
      name: "buttons",
      title: fields.buttons,
      type: "array",
      of: [{ type: "buttonObject" }],
    }),
  ],
  preview: {
    select: {
      title: "callToActionTitle",
    },
    prepare(selection) {
      const { title } = selection;
      return {
        title: title ?? fields.noTitle,
        subtitle: fields.subtitle,
        media: Clapperboard,
      };
    },
  },
});
