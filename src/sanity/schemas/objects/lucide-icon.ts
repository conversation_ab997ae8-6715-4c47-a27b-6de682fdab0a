import { defineField, defineType } from "sanity";
import { LucideIconInput } from "../../components/inputs";

export const lucideIconObject = defineType({
  name: "lucideIcon",
  title: "Lucide Icon",
  type: "object",
  fields: [
    defineField({
      name: "iconEnabled",
      title: "Enable Icon",
      type: "boolean",
      initialValue: false,
    }),
    defineField({
      name: "iconName",
      title: "Icon",
      type: "string",
      validation: (Rule) =>
        Rule.custom((value, context) => {
          const parent = context.parent as { iconEnabled?: boolean };
          if (parent?.iconEnabled && !value) {
            return "Icon name is required when icon is enabled";
          }
          return true;
        }),
      hidden: ({ parent }) => !parent?.iconEnabled,
      components: {
        input: LucideIconInput,
      },
    }),
    defineField({
      name: "size",
      title: "Size (px)",
      type: "number",
      initialValue: 24,
      validation: (Rule) => Rule.min(12).max(128).integer(),
      hidden: ({ parent }) => !parent?.iconEnabled,
      description: "Icon size in pixels (12-128)",
    }),
    defineField({
      name: "color",
      title: "Color",
      type: "string",
      description: "CSS color value (hex, rgb, named color, or CSS variable)",
      hidden: ({ parent }) => !parent?.iconEnabled,
      placeholder: "e.g., #000000, rgb(0,0,0), currentColor",
    }),
    defineField({
      name: "label",
      title: "Accessibility Label",
      type: "string",
      description:
        "Screen reader label for the icon (recommended for accessibility)",
      hidden: ({ parent }) => !parent?.iconEnabled,
    }),
  ],
  preview: {
    select: {
      iconName: "iconName",
      size: "size",
      label: "label",
    },
    prepare({ iconName, size, label }) {
      return {
        title: label || iconName || "Icon",
        subtitle: `${iconName || "No icon selected"} • ${size || 24}px`,
      };
    },
  },
});

// Simple string field alternative for basic icon usage
export const lucideIconStringField = defineField({
  name: "icon",
  title: "Icon",
  type: "string",
  components: {
    input: LucideIconInput,
  },
  validation: (Rule) => Rule.required(),
  description: "Select a Lucide icon",
});
