import { defineField } from "sanity";
import { pageReferenceTypes } from "./page-reference-types";
import { buttonFieldsDict } from "../../dictionary/studio/schemas/misc/button-fields";

const { fields } = buttonFieldsDict;

export const buttonFields = [
  defineField({
    name: "showButton",
    title: fields.showButton,
    type: "boolean",
    initialValue: false,
  }),
  defineField({
    name: "buttonText",
    title: fields.buttonText,
    type: "string",
    hidden: ({ parent }) => !parent?.showButton,
  }),
  defineField({
    name: "buttonIcon",
    title: fields.buttonIcon,
    type: "lucideIcon",
    description: "Optional icon to display with the button",
    hidden: ({ parent }) => !parent?.showButton,
  }),
  defineField({
    title: fields.buttonType,
    name: "buttonType",
    type: "string",
    options: {
      list: [
        { title: fields.internal, value: "internal" },
        { title: fields.anchor, value: "anchor" },
        { title: fields.external, value: "external" },
        { title: fields.fileDownload, value: "fileDownload" },
        { title: fields.emailAddress, value: "emailAddress" },
      ],
    },
    initialValue: "internal",
    hidden: ({ parent }) => !parent?.showButton,
  }),
  defineField({
    title: fields.buttonAnchorLocation,
    name: "buttonAnchorLocation",
    type: "string",
    options: {
      list: [
        { title: fields.currentPage, value: "currentPage" },
        { title: fields.choosePage, value: "choosePage" },
      ],
    },
    initialValue: "currentPage",
    hidden: ({ parent }) =>
      !parent?.showButton || parent?.buttonType !== "anchor",
  }),
  defineField({
    name: "buttonPageReference",
    title: fields.buttonPageReference,
    type: "reference",
    to: [...pageReferenceTypes],
    hidden: ({ parent }) =>
      !parent?.showButton ||
      (parent?.buttonType !== "internal" &&
        !(
          parent?.buttonType === "anchor" &&
          parent?.buttonAnchorLocation === "choosePage"
        )),
  }),
  defineField({
    name: "buttonAnchorId",
    title: fields.buttonAnchorId,
    type: "string",
  }),
  defineField({
    name: "buttonSearchParameters",
    title: fields.buttonSearchParameters,
    type: "array",
    of: [
      {
        type: "object",
        fields: [
          defineField({
            name: "parameterType",
            title: "Parameter Type",
            type: "string",
            options: {
              list: [
                { title: "Simple Key-Value", value: "simple" },
                { title: "JSON Object", value: "json" },
                { title: "Raw Parameter String", value: "raw" },
              ],
            },
            initialValue: "simple",
          }),
          defineField({
            name: "key",
            title: "Key",
            type: "string",
            description: "Parameter name (not used for raw parameters)",
            hidden: ({ parent }) => parent?.parameterType === "raw",
          }),
          defineField({
            name: "value",
            title: "Value",
            type: "string",
            description: "Simple string value",
            hidden: ({ parent }) => parent?.parameterType !== "simple",
          }),
          defineField({
            name: "jsonValue",
            title: "JSON Value",
            type: "text",
            description:
              'JSON object that will be URL-encoded (e.g., {"priceTo":50000,"makeModelVersions":[{"makeKey":"audi"}]})',
            rows: 4,
            validation: (Rule) =>
              Rule.custom((value, context) => {
                const parent = context.parent as any;
                if (parent?.parameterType === "json" && value) {
                  try {
                    JSON.parse(value);
                    return true;
                  } catch (e) {
                    return "Invalid JSON format";
                  }
                }
                return true;
              }),
            hidden: ({ parent }) => parent?.parameterType !== "json",
          }),
          defineField({
            name: "rawValue",
            title: "Raw Parameter String",
            type: "text",
            description:
              "Complete parameter string (e.g., query=%7B%22priceTo%22%3A50000%7D&sort=price)",
            rows: 3,
            hidden: ({ parent }) => parent?.parameterType !== "raw",
          }),
        ],
        preview: {
          select: {
            parameterType: "parameterType",
            key: "key",
            value: "value",
            jsonValue: "jsonValue",
            rawValue: "rawValue",
          },
          prepare(selection) {
            const { parameterType, key, value, jsonValue, rawValue } =
              selection;

            switch (parameterType) {
              case "simple":
                const simpleEncoded =
                  key && value
                    ? `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
                    : "";
                return {
                  title: key ? `${key}: ${value || ""}` : "Simple Parameter",
                  subtitle: simpleEncoded || "Simple Key-Value",
                };
              case "json":
                let jsonEncoded = "";
                if (key && jsonValue) {
                  try {
                    // Validate and encode the JSON
                    JSON.parse(jsonValue);
                    jsonEncoded = `${encodeURIComponent(key)}=${encodeURIComponent(jsonValue)}`;
                  } catch (e) {
                    jsonEncoded = "Invalid JSON";
                  }
                }
                return {
                  title: key ? `${key}: JSON Object` : "JSON Parameter",
                  subtitle: jsonEncoded || "JSON Object",
                };
              case "raw":
                return {
                  title: "Raw Parameters",
                  subtitle: rawValue || "Raw Parameter String",
                };
              default:
                return {
                  title: "Parameter",
                  subtitle: parameterType || "Unknown type",
                };
            }
          },
        },
      },
    ],
    hidden: ({ parent }) => !parent?.showButton,
  }),
  defineField({
    name: "buttonExternalUrl",
    title: fields.buttonExternalUrl,
    type: "url",
    validation: (Rule) =>
      Rule.uri({ scheme: ["http", "https", "mailto", "tel"] }),
    hidden: ({ parent }) =>
      !parent?.showButton || parent?.buttonType !== "external",
  }),
  defineField({
    name: "buttonEmailAddress",
    title: fields.buttonEmailAddress,
    type: "string",
    hidden: ({ parent }) =>
      !parent?.showButton || parent?.buttonType !== "emailAddress",
  }),
  defineField({
    name: "buttonFileUrl",
    title: fields.buttonFileUrl,
    type: "file",
    hidden: ({ parent }) =>
      !parent?.showButton || parent?.buttonType !== "fileDownload",
  }),
  defineField({
    title: fields.buttonVariant,
    name: "buttonVariant",
    type: "string",
    options: {
      list: [
        { title: fields.primary, value: "primary" },
        { title: fields.secondary, value: "secondary" },
        { title: fields.tertiary, value: "tertiary" },
        { title: fields.outline, value: "outline-solid" },
        { title: fields.underline, value: "underline" },
        { title: fields.tile, value: "tile" },
      ],
    },
    initialValue: "primary",
    hidden: ({ parent }) => !parent?.showButton,
  }),
  defineField({
    title: fields.buttonWidth,
    name: "buttonWidth",
    type: "string",
    options: {
      list: [
        { title: fields.auto, value: "auto" },
        { title: fields.fullWidth, value: "fullWidth" },
      ],
    },
    initialValue: "auto",
    hidden: ({ parent }) => !parent?.showButton,
  }),
];
