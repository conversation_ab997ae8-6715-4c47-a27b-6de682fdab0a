import Image from "next/image";
import { cn } from "@/lib/utils";
import type { PageBuilderType } from "@/types";
import Heading from "@/components/shared/heading";
import Container from "@/components/global/container";
import PlayVideo from "@/components/shared/play-video";
import ButtonRenderer from "@/components/shared/button-renderer";
import PortableTextEditor from "@/components/portable-text/portable-text-editor";
import { motion } from "motion/react";
import type { MediaBlockProps } from "@/components/page-builder/blocks/media-block";
import MediaBlock from "@/components/page-builder/blocks/media-block";
import VehicleSearchToolbar from "@/components/page-builder/blocks/vehicle-search-toolbar";
import { stegaClean } from "next-sanity";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import React from "react";

export type HeroBlockProps = PageBuilderType<"heroBlock">;

export default function HeroBlock(props: HeroBlockProps) {
  const {
    heading,
    content,
    blockType,
    media,
    disableVideoOnMobile,
    bottomCornerRadius,
    buttons,
    image,
    layout,
    dialogType,
    videoUrl,
    overlayType,
    enableVehicleSearch,
    vehicleSearchToolbar,
    anchorId,
  } = props;

  if (layout === "background") {
    const bgWidth = stegaClean(media?.backgroundWidth) || "fullwidth";
    const imageHeight = stegaClean(image?.height) || "full";
    console.log(imageHeight);
    return (
      <section
        {...(anchorId ? { id: anchorId } : {})}
        className={cn("pattern-bg border-b border-b-border pt-8 xl:pt-0", {
          "rounded-3xl md:rounded-4xl": bottomCornerRadius === "rounded-sm",
          "px-4 md:px-10": bgWidth === "contained",
        })}
      >
        <Container
          variant={bgWidth === "contained" ? "contained" : "fullWidth"}
          className={cn("relative border-x border-dashed", {
            "p-0!": blockType,
          })}
        >
          <div
            className={cn(
              "absolute z-2 inset-0 overflow-hidden flex flex-col justify-start lg:justify-center pointer-events-none pt-24! p-4 md:p-8 lg:px-20!",
            )}
          >
            <div className="pb-6">
              <Heading
                size="xxxl"
                tag="h1"
                className="lg:max-w-120 xl:max-w-160 text-balance leading-tight"
              >
                {heading}
              </Heading>
            </div>
            <PortableTextEditor
              data={content ?? []}
              classNames="lg:max-w-120 xl:max-w-160 lg:mt-3 text-lg lg:text-xl text-foreground/80 text-balance"
            />
            {buttons && buttons.length > 0 && (
              <div className="mt-6 lg:mt-10">
                <ButtonRenderer
                  buttons={buttons}
                  classNames="pointer-events-auto"
                />
              </div>
            )}
          </div>

          {/* Vehicle Search Toolbar - positioned right on desktop only */}
          {vehicleSearchToolbar && enableVehicleSearch && (
            <div className="absolute inset-0 h-full z-3 hidden lg:block pointer-events-none pt-22">
              <div className="flex items-center justify-end w-full h-full p-4">
                <VehicleSearchToolbar {...vehicleSearchToolbar} />
              </div>
            </div>
          )}

          {blockType === "media" && media && (
            <div
              className={cn("relative h-full w-full", {
                "hidden xl:block": disableVideoOnMobile && image,
                "max-h-180 lg:max-h-none":
                  stegaClean(image?.height) === "short",
              })}
            >
              <MediaBlock
                {...(media as unknown as MediaBlockProps)}
                muxVideo={media.muxVideo}
                isBackground
                overlayType="dark"
              />
            </div>
          )}
          {image && (
            <div
              className={cn("overflow-hidden relative h-full w-full", {
                "xl:hidden": blockType !== "banner",
                "min-h-120 lg:min-h-200": imageHeight === "short",
                "lg:min-h-screen": imageHeight === "full",
              })}
            >
              <Image
                priority
                fill
                src={image?.asset?.url ?? ""}
                alt={image?.asset?.altText ?? ""}
                className={cn("object-cover", {
                  "lg:max-h-none": enableVehicleSearch,
                })}
              />
              {overlayType === "dark" && <DarkOverlay />}
              {dialogType === "video" && videoUrl && (
                <div>
                  <PlayVideo videoUrl={videoUrl} />
                </div>
              )}
            </div>
          )}
          {/* On mobile only, we want to render the vehicle search toolbar below the hero block */}
          {vehicleSearchToolbar && enableVehicleSearch && (
            <>
              <div className="block lg:hidden fixed bottom-0 left-0 right-0 w-full z-50">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button
                      variant="primary"
                      className="w-full h-18 rounded-none text-lg"
                    >
                      <Search className="h-5 w-5 lg:h-7 lg:w-7 mr-2" />
                      Rechercher un véhicule
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="bottom">
                    <VehicleSearchToolbar {...vehicleSearchToolbar} />
                  </SheetContent>
                </Sheet>
              </div>
            </>
          )}
        </Container>
      </section>
    );
  }

  return (
    <section
      {...(anchorId ? { id: anchorId } : {})}
      className={cn("px-4 md:px-10 pattern-bg border-b border-b-border", {
        "rounded-3xl md:rounded-4xl": bottomCornerRadius === "rounded-sm",
      })}
    >
      <Container
        className={cn("space-y-10 xl:-space-y-6 border-x border-dashed", {
          "pb-7 md:pb-12": blockType,
        })}
      >
        <div
          className={cn(
            "pt-4 md:pt-12 pb-16 md:pb-24 xl:pb-36 @container grid grid-cols-12 gap-3 md:gap-6 xl:gap-14 md:px-14 md:border-x md:border-dashed",
            {
              "pb-6": blockType,
            },
          )}
        >
          <div className="col-span-12 @xl:col-span-7">
            <Heading
              size="xxxl"
              tag="h1"
              className="md:max-w-160 text-balance leading-tight"
            >
              {heading}
            </Heading>
          </div>
          <div className="col-span-12 @xl:col-span-5">
            <PortableTextEditor
              data={content ?? []}
              classNames="mt-3 md:text-lg text-muted-foreground text-balance"
            />
            {buttons && buttons.length > 0 && (
              <div className="mt-8 md:mt-10">
                <ButtonRenderer buttons={buttons} />
              </div>
            )}
          </div>
        </div>
        {blockType === "banner" && image && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
            className="p-4 md:p-6 border border-dashed pattern-bg"
          >
            <div className="overflow-hidden relative h-full w-full">
              <Image
                priority
                width={1400}
                height={800}
                src={image?.asset?.url ?? ""}
                alt={image?.asset?.altText ?? ""}
                className={cn("object-cover rounded-2xl md:rounded-3xl", {
                  "max-h-120": stegaClean(image?.height) === "short",
                })}
              />
              {overlayType === "dark" && <DarkOverlay />}
              {dialogType === "video" && videoUrl && (
                <div>
                  <PlayVideo videoUrl={videoUrl} />
                </div>
              )}
            </div>
          </motion.div>
        )}
        {blockType === "media" && media && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
            className="border border-dashed pattern-bg"
          >
            <MediaBlock {...(media as unknown as MediaBlockProps)} />
          </motion.div>
        )}
        {/* On mobile and desktop for this variant, we want to render the vehicle search toolbar below the hero block */}
        {vehicleSearchToolbar && enableVehicleSearch && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, ease: [0.25, 0.1, 0.25, 1] }}
            className="p-4 md:p-6 border border-dashed pattern-bg"
          >
            <div className="bg-white dark:bg-gray-900 rounded-2xl p-6">
              <VehicleSearchToolbar {...vehicleSearchToolbar} />
            </div>
          </motion.div>
        )}
      </Container>
    </section>
  );
}

function DarkOverlay() {
  return (
    <>
      <div className="absolute inset-0 bg-foreground dark:bg-background opacity-10" />
      <div className="absolute bottom-0 left-0 right-0 bg-linear-to-t from-foreground dark:from-background  via-background/80 to-background/10 h-full w-full opacity-90" />
    </>
  );
}
