import type { StaticImageData } from "next/image";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { stegaClean } from "next-sanity";
import type { PageBuilderType } from "@/types";
import Container from "@/components/global/container";
import PlayVideo from "@/components/shared/play-video";
import NextVideoPlayer from "@/components/shared/next-video-player";
import type { MaxResolutionValue } from "@mux/playback-core";

export type MediaBlockProps = PageBuilderType<"mediaBlock">;

export default function MediaBlock(
  props: MediaBlockProps & {
    isBackground?: boolean;
  },
) {
  const {
    backgroundType,
    backgroundWidth,
    image,
    muxVideo,
    overlayType,
    dialogType,
    videoUrl,
    isBackground = false,
    anchorId,
  } = props;

  const cleanBackgroundType = stegaClean(backgroundType);
  const cleanBackgroundWidth = stegaClean(backgroundWidth);
  const cleanOverlayType = stegaClean(overlayType);
  const cleanDialogType = stegaClean(dialogType);

  return (
    <section
      {...(anchorId ? { id: anchorId } : {})}
      className={cn("border-t border-dashed pattern-bg h-full min-w-full")}
    >
      <Container
        variant={
          cleanBackgroundWidth && cleanBackgroundWidth === "contained"
            ? "contained"
            : "fullWidth"
        }
        className={cn("relative overflow-hidden px-0", {
          "border-x border-dashed": cleanBackgroundWidth === "contained",
          "px-4 md:px-10": cleanBackgroundWidth === "contained",
        })}
      >
        {/* Image Background */}
        {cleanBackgroundType === "image" && image && (
          <div
            className={cn("relative inset-0 w-full h-full min-h-120", {
              "min-h-140": isBackground,
            })}
          >
            <Image
              src={image?.asset?.url ?? ""}
              fill
              alt={image?.asset?.altText ?? ""}
              className="object-cover"
            />
            {cleanOverlayType === "dark" && <DarkOverlay />}
          </div>
        )}

        {/* Mux Video Background */}
        {cleanBackgroundType === "muxVideo" && muxVideo && (
          <div
            className={cn("", {
              "min-h-140": isBackground,
            })}
          >
            <NextVideoPlayer
              video={{
                ...muxVideo,
                title: muxVideo.title!,
                description: muxVideo.description!,
                poster: muxVideo.poster as unknown as StaticImageData,
                controls: muxVideo.controls!,
                autoplay: muxVideo.autoplay!,
                loop: muxVideo.loop!,
                muted: muxVideo.autoplay! ?? muxVideo.muted!,
                aspectRatio: muxVideo.aspectRatio!,
                maxResolution: muxVideo.maxResolution as MaxResolutionValue,
                captions: muxVideo.captions!,
                language: muxVideo.language!,
                // @ts-expect-error: incorrect null check
                video: muxVideo.video,
              }}
              overlayType={cleanOverlayType!}
              isBackground={isBackground}
              className="w-full h-full"
            />
          </div>
        )}

        {/* Video Dialog Overlay */}
        {cleanDialogType === "video" && videoUrl && (
          <PlayVideo videoUrl={videoUrl} />
        )}
      </Container>
    </section>
  );
}

function DarkOverlay() {
  return (
    <>
      <div className="absolute inset-0 bg-foreground opacity-20" />
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-foreground to-transparent h-[50%] w-full opacity-15" />
    </>
  );
}
