# Vehicle Card System

A comprehensive, refactored vehicle card system following SOLID principles, TypeScript best practices, and modern React patterns. This system provides flexible, responsive, and accessible vehicle display components with multiple variants and layouts.

## 🏗️ Architecture

### SOLID Principles Implementation

- **Single Responsibility**: Each component has one clear purpose
- **Open/Closed**: Extensible through props and composition
- **Liskov Substitution**: Consistent interfaces across variants
- **Interface Segregation**: Separated concerns into distinct interfaces
- **Dependency Inversion**: Components depend on abstractions

### Component Structure

```
src/components/vehicles/card/
├── vehicle-card-refactored.tsx    # Main orchestrator component
├── vehicle-card-image.tsx         # Image handling with overlays
├── vehicle-card-content.tsx       # Title, price, basic info
├── vehicle-card-specs.tsx         # Technical specifications
├── vehicle-card-actions.tsx       # Contact/interaction buttons
├── vehicle-card-grid.tsx          # Layout components
├── types.ts                       # TypeScript interfaces
├── constants.ts                   # Configuration constants
├── utils.ts                       # Pure utility functions
├── hooks/
│   └── use-vehicle-card.ts        # State management hooks
├── examples/
│   └── vehicle-card-showcase.tsx  # Usage examples
└── README.md                      # This file
```

## 🎨 Variants

### Default Variant
Standard vehicle card for general listings.
```tsx
<VehicleCard variant="default" vehicle={vehicle} />
```

### Featured Variant
Enhanced card for premium or highlighted vehicles.
```tsx
<VehicleCard variant="featured" vehicle={vehicle} />
```

### Compact Variant
Condensed card for dense layouts.
```tsx
<VehicleCard variant="compact" vehicle={vehicle} />
```

## 📱 Responsive Design

Following the visual structure guidelines from `vehicle_search_visual_structure.md`:

- **Mobile**: Single column, compact variant
- **Tablet**: Two columns, default variant
- **Desktop**: 3-4 columns, default/featured variants

## 🚀 Usage Examples

### Basic Usage

```tsx
import { VehicleCard } from '@/components/vehicles/card';

function VehicleList({ vehicles }) {
  return (
    <div>
      {vehicles.map((vehicle) => (
        <VehicleCard
          key={vehicle.id}
          vehicle={vehicle}
          onContactClick={(id) => console.log('Contact:', id)}
        />
      ))}
    </div>
  );
}
```

### Grid Layout

```tsx
import { VehicleCardGrid } from '@/components/vehicles/card';

function VehiclesPage({ vehicles }) {
  return (
    <VehicleCardGrid
      vehicles={vehicles}
      variant="default"
      onVehicleContact={(id) => handleContact(id)}
    />
  );
}
```

### Featured Vehicles Section

```tsx
import { FeaturedVehicles } from '@/components/vehicles/card';

function HomePage({ featuredVehicles }) {
  return (
    <FeaturedVehicles
      vehicles={featuredVehicles}
      title="Coups de cœur"
      maxItems={3}
    />
  );
}
```

### Carousel Display

```tsx
import { VehicleCarousel } from '@/components/vehicles/card';

function SimilarVehicles({ vehicles }) {
  return (
    <VehicleCarousel
      vehicles={vehicles}
      variant="compact"
    />
  );
}
```

### Using Hooks for State Management

```tsx
import { useVehicleCard } from '@/components/vehicles/card';

function CustomVehicleCard({ vehicle }) {
  const {
    isFavorited,
    handleFavoriteToggle,
    handleContactClick,
    vehicleData
  } = useVehicleCard({
    vehicle,
    onFavoriteChange: (id, favorited) => {
      // Save to backend/localStorage
    }
  });

  return (
    <VehicleCard
      vehicle={vehicle}
      isFavorited={isFavorited}
      onFavoriteToggle={handleFavoriteToggle}
      onContactClick={handleContactClick}
    />
  );
}
```

## 🎯 Features

### Adaptive Specifications Display
Vehicle specs are intelligently prioritized based on vehicle type:

- **Sport**: Power, fuel type, transmission, drive type
- **Family**: Seats, fuel type, doors, CO₂ emissions
- **Electric**: Power, CO₂ emissions, seats, transmission
- **Luxury**: Power, fuel type, seats, drive type
- **Economy**: Fuel type, CO₂ emissions, doors, seats

### Automatic Featured Detection
Vehicles are automatically marked as featured based on:
- High price (>100,000 CHF)
- New condition
- Recent registration (<1 year)

### Responsive Images
Optimized image loading with:
- Next.js Image optimization
- Proper aspect ratios
- Fallback placeholders
- Hover animations

### Accessibility
- Proper ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance

## 🛠️ Configuration

### Vehicle Type Detection
```tsx
// Customize in utils.ts
export function determineVehicleType(vehicle) {
  // Your custom logic here
}
```

### Specification Configuration
```tsx
// Customize in constants.ts
export const VEHICLE_SPEC_CONFIGS = {
  customSpec: {
    key: 'customSpec',
    label: 'Custom Label',
    icon: 'custom-icon',
    formatter: (value) => `${value} units`,
    priority: 1,
    vehicleTypes: ['sport', 'luxury']
  }
};
```

### Styling Customization
```tsx
// Customize in constants.ts
export const CUSTOM_STYLES = {
  featured: {
    border: 'border-2 border-custom-color',
    glow: 'shadow-lg shadow-custom-color/20'
  }
};
```

## 🎨 Design System Integration

### Colors
Following the Autocorner design system:
- Primary: Yellow accent (#DFFF00)
- Dark theme support
- Semantic condition colors

### Typography
- Responsive text scaling
- Proper font weights
- Accessible contrast ratios

### Spacing
- Consistent spacing scale
- Responsive padding/margins
- Grid gap configurations

## 🔧 Development

### Adding New Variants

1. Update the `variant` type in `types.ts`
2. Add configuration to `constants.ts`
3. Implement variant logic in components
4. Add grid configuration if needed
5. Update documentation

### Custom Specifications

1. Add spec config to `VEHICLE_SPEC_CONFIGS`
2. Update `VehicleCardSpecsProps` interface
3. Add icon mapping if needed
4. Test across vehicle types

### Performance Considerations

- Components use `React.memo` where appropriate
- Expensive calculations are memoized
- Images are lazy-loaded
- Event handlers use `useCallback`

## 🧪 Testing

### Component Testing
```tsx
import { render, screen } from '@testing-library/react';
import { VehicleCard } from '@/components/vehicles/card';

test('displays vehicle information', () => {
  const vehicle = {
    id: '1',
    make: { name: 'BMW' },
    model: { name: 'X3' },
    price: 45000
  };

  render(<VehicleCard vehicle={vehicle} />);
  
  expect(screen.getByText('BMW X3')).toBeInTheDocument();
  expect(screen.getByText('CHF 45'000')).toBeInTheDocument();
});
```

### Integration Testing
```tsx
import { VehicleCardGrid } from '@/components/vehicles/card';

test('renders grid of vehicles', () => {
  const vehicles = [/* array of vehicles */];
  
  render(<VehicleCardGrid vehicles={vehicles} />);
  
  expect(screen.getAllByRole('link')).toHaveLength(vehicles.length);
});
```

## 📚 Related Documentation

- [Vehicle Search Visual Structure](../../../doc/llm/ui/vehicle_search_visual_structure.md)
- [Vehicle Search Design Guidelines](../../../doc/llm/ui/vehicle_search_design_guidelines.md)
- [Autocorner Development Standards](../../../doc/llm/development/)
- [SOLID Principles Implementation](../../../doc/llm/development/solid-principles.md)

## 🤝 Contributing

1. Follow SOLID principles
2. Maintain TypeScript strict mode compliance
3. Add proper JSDoc comments
4. Include tests for new features
5. Update documentation
6. Follow existing naming conventions

## 🔄 Migration from Old Component

### Before (Old Implementation)
```tsx
import { VehicleCard } from '@/components/vehicles/card/vehicle-card';

<VehicleCard vehicle={vehicle} index={index} />
```

### After (New Implementation)
```tsx
import { VehicleCard } from '@/components/vehicles/card';

<VehicleCard 
  vehicle={vehicle} 
  variant="default"
  onContactClick={handleContact}
  showSpecs={true}
/>
```

The new implementation is backward compatible but offers enhanced functionality and better performance.
