/**
 * Vehicle Card Grid Component
 * Following Autocorner design patterns with proper spacing
 * Using actual AutoScout generated types
 */
import React, { Suspense } from "react";
import { cn } from "@/lib/utils";
import type { Schemas } from "@/lib/api/autoscout/types/generated";
import { VehicleCard } from "./vehicle-card";
import { useVehicleCardList } from "./hooks/use-vehicle-card";

type ListingBaseResponse = Schemas.ListingBaseResponse;

interface VehicleCardGridProps {
  vehicles: ListingBaseResponse[]; // Remove undefined from array
  variant?: "default" | "featured" | "compact";
  className?: string;
  onVehicleContact?: (vehicleId: string | number) => void;
  maxItems?: number;
}

/**
 * Responsive grid component for displaying vehicle cards
 * Clean spacing and proper responsive behavior
 */
export function VehicleCardGrid({
  vehicles,
  variant = "default",
  className,
  onVehicleContact,
  maxItems,
}: VehicleCardGridProps) {
  const { handleFavoriteChange, isVehicleFavorited, trackVehicleContact } =
    useVehicleCardList();

  // Handle contact click with analytics
  const handleContactClick = React.useCallback(
    (vehicleId: string | number) => {
      trackVehicleContact(vehicleId);
      onVehicleContact?.(vehicleId);
    },
    [trackVehicleContact, onVehicleContact],
  );

  // Filter out undefined vehicles and limit if maxItems is specified
  const displayVehicles = React.useMemo(() => {
    const validVehicles = vehicles.filter(
      (vehicle): vehicle is ListingBaseResponse =>
        vehicle !== undefined && vehicle !== null,
    );
    return maxItems ? validVehicles.slice(0, maxItems) : validVehicles;
  }, [vehicles, maxItems]);

  if (!displayVehicles.length) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground text-lg mb-2">
          Aucun véhicule trouvé
        </div>
        <div className="text-muted-foreground text-sm">
          Essayez de modifier vos critères de recherche
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid gap-x-6 gap-y-6 grid-cols-1",
        // Following the same responsive pattern as VehicleModelCard
        "md:grid-cols-2",
        "xl:grid-cols-3",
        // Compact variant gets more columns
        variant === "compact" && "md:grid-cols-4",
        // Featured gets fewer columns for more space
        variant === "featured" && "md:grid-cols-2",
        className,
      )}
    >
      {displayVehicles.map((vehicle, index) => (
        <Suspense
          key={`suspense-${vehicle.id}-${index}`}
          fallback={<div className="h-96 bg-muted animate-pulse rounded-lg" />}
        >
          <VehicleCard
            key={`vehicle-${vehicle.id}-${index}`}
            vehicle={vehicle}
            variant={variant}
            isFavorited={isVehicleFavorited(vehicle.id || 0)}
            onFavoriteToggle={() =>
              handleFavoriteChange(
                vehicle.id as number,
                isVehicleFavorited(vehicle.id as number),
              )
            }
            onContactClick={handleContactClick}
            priority={index < 4} // First 4 images get priority loading
          />
        </Suspense>
      ))}
    </div>
  );
}

/**
 * Featured Vehicles Section
 * Special layout for highlighted vehicles
 */
interface FeaturedVehiclesProps {
  vehicles: ListingBaseResponse[];
  title?: string;
  subtitle?: string;
  maxItems?: number;
  className?: string;
  onVehicleContact?: (vehicleId: string | number) => void;
}

export function FeaturedVehicles({
  vehicles,
  title = "Véhicules mis en avant",
  subtitle,
  maxItems = 3,
  className,
  onVehicleContact,
}: FeaturedVehiclesProps) {
  // Filter for featured-worthy vehicles
  const featuredVehicles = React.useMemo(() => {
    return vehicles
      .filter(
        (vehicle): vehicle is ListingBaseResponse =>
          vehicle !== undefined &&
          vehicle !== null &&
          ((vehicle.price && vehicle.price > 50000) ||
            vehicle.conditionType === "new"),
      )
      .slice(0, maxItems);
  }, [vehicles, maxItems]);

  if (!featuredVehicles.length) {
    return null;
  }

  return (
    <section className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold text-foreground">{title}</h2>
        {subtitle && (
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>

      {/* Featured Grid */}
      <VehicleCardGrid
        vehicles={featuredVehicles}
        variant="featured"
        onVehicleContact={onVehicleContact}
        className="max-w-6xl mx-auto"
      />
    </section>
  );
}

/**
 * Vehicle Carousel Component
 * Horizontal scrolling layout for showcasing vehicles
 */
interface VehicleCarouselProps {
  vehicles: ListingBaseResponse[];
  variant?: "default" | "compact";
  className?: string;
  onVehicleContact?: (vehicleId: string | number) => void;
}

export function VehicleCarousel({
  vehicles,
  variant = "default",
  className,
  onVehicleContact,
}: VehicleCarouselProps) {
  const { handleFavoriteChange, isVehicleFavorited, trackVehicleContact } =
    useVehicleCardList();

  const handleContactClick = React.useCallback(
    (vehicleId: string | number) => {
      trackVehicleContact(vehicleId);
      onVehicleContact?.(vehicleId);
    },
    [trackVehicleContact, onVehicleContact],
  );

  // Filter out undefined vehicles
  const validVehicles = vehicles.filter(
    (vehicle): vehicle is ListingBaseResponse =>
      vehicle !== undefined && vehicle !== null,
  );

  if (!validVehicles.length) {
    return null;
  }

  return (
    <div className={cn("overflow-x-auto", className)}>
      <div
        className={cn(
          "flex gap-6 pb-4",
          "snap-x snap-mandatory",
          "scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",
        )}
      >
        {validVehicles.map((vehicle, index) => (
          <div
            key={`carousel-vehicle-${vehicle.id}-${index}`}
            className={cn(
              "flex-shrink-0 snap-start",
              variant === "compact" ? "w-80" : "w-96",
            )}
          >
            <Suspense
              fallback={
                <div className="h-96 bg-muted animate-pulse rounded-lg" />
              }
            >
              <VehicleCard
                vehicle={vehicle}
                variant={variant}
                isFavorited={isVehicleFavorited(vehicle.id || 0)}
                onFavoriteToggle={() =>
                  handleFavoriteChange(
                    vehicle.id as number,
                    isVehicleFavorited(vehicle.id as number),
                  )
                }
                onContactClick={handleContactClick}
              />
            </Suspense>
          </div>
        ))}
      </div>
    </div>
  );
}
