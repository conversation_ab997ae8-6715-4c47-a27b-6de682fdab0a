"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Building, Car, Crown } from "lucide-react";

interface CenterTypeFilterProps {
  currentType?: string;
}

export function CenterTypeFilter({ currentType }: CenterTypeFilterProps) {
  const centerTypes = [
    {
      value: "",
      label: "Tous les centres",
      icon: Building,
      description: "Voir tous nos centres",
    },
    {
      value: "audi",
      label: "Audi",
      icon: Car,
      description: "Centres spécialisés Audi",
    },
    {
      value: "skoda",
      label: "Skoda",
      icon: Car,
      description: "Centres spécialisés Skoda",
    },
    {
      value: "occasions",
      label: "Occasions",
      icon: Crown,
      description: "Véhicules d'haut de gamme",
    },
  ];

  return (
    <div className="mb-8">
      <div className="flex flex-wrap gap-2 justify-center">
        {centerTypes.map((type) => {
          const Icon = type.icon;
          const isActive =
            currentType === type.value || (!currentType && type.value === "");

          return (
            <Button
              key={type.value}
              asChild
              variant={isActive ? "primary" : "outline"}
              className="flex items-center gap-2"
            >
              <Link
                href={type.value ? `/centres?type=${type.value}` : "/centres"}
                title={type.description}
              >
                <Icon className="h-4 w-4" />
                {type.label}
              </Link>
            </Button>
          );
        })}
      </div>
    </div>
  );
}
