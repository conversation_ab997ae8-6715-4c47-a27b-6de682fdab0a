import Link from "next/link";
import { ChevronRight } from "lucide-react";
import Container from "@/components/global/container";
import { CentersGrid } from "./centers-grid";
import Heading from "@/components/shared/heading";
import AnimatedUnderline from "@/components/shared/animated-underline";

interface CentersShowcaseBlockProps {
  title?: string;
  subtitle?: string;
  centerType?: string;
  limit?: number;
  showViewAll?: boolean;
  className?: string;
}

export async function CentersShowcaseBlock({
  title = "Nos Centres Autocorner",
  subtitle = "Découvrez nos centres spécialisés près de chez vous",
  centerType,
  limit = 6,
  showViewAll = true,
  className,
}: CentersShowcaseBlockProps) {
  return (
    <section className={`py-16 ${className || ""}`}>
      <Container>
        {/* Header */}
        <div className="text-center mb-12 relative">
          <div className="p-6 md:p-8 rounded-3xl border border-dashed backdrop-blur-md backdrop-opacity-50 pattern-bg">
            <Heading tag="h2" size="xl" className="mb-4 text-balance">
              {title}
            </Heading>
            {subtitle && (
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-balance">
                {subtitle}
              </p>
            )}
          </div>
          <AnimatedUnderline className="mt-2" />
        </div>

        {/* Centers Grid */}
        <CentersGrid centerType={centerType} limit={limit} className="mb-8" />

        {/* View All Button */}
        {showViewAll && (
          <div className="text-center">
            <Link
              href="/centres"
              className="inline-flex items-center gap-2 text-lg font-medium relative group"
            >
              <span>Voir tous nos centres</span>
              <ChevronRight
                size={18}
                className="transform group-hover:translate-x-1 transition-transform duration-300"
              />
              <AnimatedUnderline className="mt-1" />
            </Link>
          </div>
        )}
      </Container>
    </section>
  );
}
