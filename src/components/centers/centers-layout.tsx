import type { ReactNode } from "react";
import Container from "@/components/global/container";

interface CentersLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

export function CentersLayout({
  children,
  title = "Nos Centres Autocorner",
  description = "Nous plaçons la mobilité et la satisfaction client au cœur de nos priorités. Forts d’une expertise reconnue et d’une présence locale, nous vous accueillons dans nos différents centres, chacun dédié à une expérience unique.",
}: CentersLayoutProps) {
  return (
    <main className="pt-24">
      <Container className="py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">{title}</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            {description}
          </p>
        </div>
        {children}
      </Container>
    </main>
  );
}
