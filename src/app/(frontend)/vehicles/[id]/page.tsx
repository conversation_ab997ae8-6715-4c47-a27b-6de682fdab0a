import { notFound } from "next/navigation";
import { Suspense } from "react";
import type { Metadata } from "next";
import type { Schemas } from "@/lib/api/autoscout/types/generated";
import { caller } from "@/lib/trpc/adapters/caller";
import { sanityFetch } from "@/sanity/lib/live";
import { brandBySlugQuery } from "@/sanity/lib/queries/documents/brand";
import { vehicleModelBySlugQuery } from "@/sanity/lib/queries/documents/vehicleModel";
import VehicleGallery from "./_components/vehicle-gallery";
import VehicleSpecifications from "./_components/vehicle-specifications";
import VehicleSellerInfo from "./_components/vehicle-seller-info";
import VehiclePricing from "./_components/vehicle-pricing";
import BackButton from "@/components/shared/back-button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Container from "@/components/global/container";
import Image from "next/image";
import Link from "next/link";
import VehicleTitle from "./_components/vehicle-title";
import { Button } from "@/components/ui/button";
import { getBrandColor } from "@/components/brands/models/utils";

interface VehicleDetailPageProps {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({
  params,
}: VehicleDetailPageProps): Promise<Metadata> {
  try {
    const paramsData = await params;

    const vehicle = await caller.autoscout.listings.getById({
      id: paramsData.id,
    });

    if (!vehicle) {
      return {
        title: "Vehicle Not Found",
      };
    }

    return {
      title: `${vehicle.make?.name} ${vehicle.model?.name} - Autocorner`,
      description:
        vehicle.teaser ||
        `${vehicle.make?.name} ${vehicle.model?.name} ${vehicle.firstRegistrationYear || ""}`,
    };
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return {
      title: "Vehicle Details - Autocorner",
    };
  }
}

// Helper function to convert a string to slug format
function slugify(text: string): string {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, "-") // Replace spaces with -
    .replace(/&/g, "-and-") // Replace & with 'and'
    .replace(/[^\w\-]+/g, "") // Remove all non-word characters
    .replace(/\-\-+/g, "-"); // Replace multiple - with single -
}

export default async function VehicleDetailPage({
  params,
}: VehicleDetailPageProps) {
  let vehicle: Schemas.ListingResponse | null = null;
  let vehicleEquipment: Schemas.ListingEquipmentResponse | null = null;
  let similarVehicles: Schemas.ListingBaseResponse[] = [];
  let brandData = null;
  let modelData = null;
  const paramsData = await params;

  try {
    // Fetch vehicle data using tRPC
    vehicle = await caller.autoscout.listings.getById({ id: paramsData.id });

    // Fetch similar vehicles if vehicle data is available
    if (vehicle) {
      const vehicleEquipmentData = await caller.autoscout.listings.getEquipment(
        {
          id: paramsData.id,
        },
      );

      if (vehicleEquipmentData) {
        vehicleEquipment = vehicleEquipmentData;
      }

      const similarVehiclesData =
        await caller.autoscout.listings.getSimilarVehicles({
          id: paramsData.id,
          limit: 7,
        });

      if (similarVehiclesData) {
        similarVehicles =
          similarVehiclesData.content?.filter(
            (similarVehicle) => similarVehicle.id !== vehicle?.id,
          ) || [];
      }

      // Fetch brand and model data from Sanity CMS if available
      if (vehicle.make?.name) {
        const brandSlugFromName = slugify(vehicle.make.name);
        const brandResult = await sanityFetch({
          query: brandBySlugQuery,
          params: {
            slug: brandSlugFromName,
          },
          stega: false,
        });
        brandData = brandResult.data;

        // Fetch model data if brand data is available and model name exists
        if (brandData && vehicle.model?.name) {
          const modelSlugFromName = slugify(vehicle.model.name);
          const modelResult = await sanityFetch({
            query: vehicleModelBySlugQuery,
            params: {
              slug: modelSlugFromName,
            },
            stega: false,
          });
          modelData = modelResult.data;
        }
      }
    }
  } catch (error) {
    console.error("Error fetching vehicle:", error);
  }

  if (!vehicle) {
    notFound();
  }

  // Determine if we can link to a brand/model page
  const brandSlug = brandData?.slug || vehicle.make?.key?.toLowerCase();
  const modelSlug = modelData?.slug || vehicle.model?.key?.toLowerCase();
  const hasModelPage = !!(brandSlug && modelSlug);

  // Get brand colors from Sanity CMS if available
  const brandColor = getBrandColor(brandData?.brandColors?.primaryColor);

  return (
    <Container>
      <div
        className="py-8 max-w-10xl mx-auto pt-28 lg:pt-32"
        style={{
          "--brand-color-primary": brandColor,
        }}
      >
        <div className="mb-6">
          <BackButton href="/search" title="Retour à la recherche" />
        </div>

        <div className="grid grid-cols-12 gap-8">
          {/* Left Column - Gallery and Seller Info */}
          <div className="col-span-12 lg:col-span-8 space-y-8">
            <Suspense
              fallback={
                <div className="h-96 bg-muted animate-pulse rounded-lg"></div>
              }
            >
              <VehicleGallery vehicle={vehicle} />
            </Suspense>

            <VehicleTitle vehicle={vehicle} />

            <div className="lg:hidden">
              <VehiclePricing vehicle={vehicle} />
            </div>

            <Tabs defaultValue="specifications" className="w-full">
              <TabsList className="w-full grid grid-cols-3">
                <TabsTrigger value="specifications">Spécifications</TabsTrigger>
                <TabsTrigger value="equipment">Équipement</TabsTrigger>
                <TabsTrigger value="center">Centre</TabsTrigger>
              </TabsList>
              <TabsContent value="specifications" className="mt-6">
                <VehicleSpecifications vehicle={vehicle} />
              </TabsContent>
              <TabsContent value="equipment" className="mt-6">
                <div className="bg-card rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-4">
                    Équipement du véhicule
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {vehicleEquipment &&
                    vehicleEquipment.standard &&
                    vehicleEquipment.standard?.length > 0 ? (
                      <div className="space-y-4">
                        <h4 className="font-semibold">Équipement de série</h4>
                        <ul className="space-y-2">
                          {vehicleEquipment.standard.map((equipment, index) => (
                            <li
                              key={index}
                              className="text-sm text-muted-foreground"
                            >
                              {equipment.name}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ) : (
                      <p>
                        Aucun équipement de série répertorié pour ce véhicule.
                      </p>
                    )}
                    {vehicleEquipment &&
                    vehicleEquipment.optional &&
                    vehicleEquipment.optional?.length > 0 ? (
                      <div className="space-y-4">
                        <h4 className="font-semibold">Équipement en option</h4>
                        <ul className="space-y-2">
                          {vehicleEquipment.optional.map((equipment, index) => (
                            <li
                              key={index}
                              className="text-sm text-muted-foreground"
                            >
                              {equipment.name}
                            </li>
                          ))}
                        </ul>
                      </div>
                    ) : (
                      <p>
                        Aucun équipement en option répertorié pour ce véhicule.
                      </p>
                    )}
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="center" className="mt-6">
                <Suspense
                  fallback={<div>Chargement des informations du centre...</div>}
                >
                  <VehicleSellerInfo vehicle={vehicle} />
                </Suspense>
              </TabsContent>
            </Tabs>
          </div>

          {/* Right Column - Pricing and Quick Actions */}
          <div className="col-span-12 lg:col-span-4 space-y-6">
            <div className="sticky top-24">
              <VehiclePricing vehicle={vehicle} className="hidden lg:block" />

              {hasModelPage && (
                <div className="mt-6 bg-card rounded-lg p-6">
                  <h3 className="text-lg font-bold mb-3">
                    Informations sur le modèle
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    En savoir plus sur le modèle {vehicle.make?.name}{" "}
                    {vehicle.model?.name}
                  </p>
                  <Link
                    href={`/gammes/${brandSlug}/modeles/${modelSlug}?backLink=/vehicles/${vehicle.id}&backLinkLabel=Retour%20au%20véhicule`}
                    className="block w-full py-2 px-4 rounded text-center transition-colors"
                  >
                    <Button variant="primary" className="w-full brand">
                      Voir les détails du modèle
                    </Button>
                  </Link>
                  {brandData && (
                    <div className="mt-4 flex items-center justify-between">
                      <Link
                        href={`/gammes/${brandSlug}?backLink=/vehicles/${vehicle.id}`}
                        className="flex items-center space-x-2"
                      >
                        {brandData.logo?.asset?.url && (
                          <Image
                            src={brandData.logo.asset.url}
                            alt={brandData.name}
                            className="w-full h-full object-cover"
                            width={40}
                            height={40}
                          />
                        )}
                        <span className="text-sm font-medium">
                          {brandData.name}
                        </span>
                      </Link>
                      {modelData && (
                        <span className="text-sm text-muted-foreground">
                          {modelData.modelYear || ""}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Similar Vehicles Section */}
        {similarVehicles.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold mb-6">Véhicules similaires</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {similarVehicles.map((similarVehicle, index) => (
                <div
                  key={`similar-${similarVehicle.id}-${index}`}
                  className="border rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                >
                  <a href={`/vehicles/${similarVehicle.id}`}>
                    <div className="h-48 overflow-hidden">
                      {similarVehicle.images &&
                      similarVehicle.images.length > 0 ? (
                        <Image
                          src={similarVehicle.images[0].url || ""}
                          alt={`${similarVehicle.make?.name} ${similarVehicle.model?.name}`}
                          className="w-full h-full object-cover"
                          width={400}
                          height={288}
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <span className="text-muted-foreground">
                            No image available
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-bold text-lg">
                        {similarVehicle.make?.name} {similarVehicle.model?.name}
                      </h3>
                      <p className="text-gray-600">
                        {similarVehicle.firstRegistrationYear}
                        {similarVehicle.mileage &&
                          ` • ${similarVehicle.mileage.toLocaleString("fr-CH")} km`}
                      </p>
                      {similarVehicle.price && (
                        <p className="font-bold text-xl mt-2">
                          {new Intl.NumberFormat("fr-CH", {
                            style: "currency",
                            currency: "CHF",
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 2,
                          }).format(similarVehicle.price)}
                        </p>
                      )}
                    </div>
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Container>
  );
}
