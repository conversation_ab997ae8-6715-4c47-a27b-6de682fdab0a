import type { Schemas } from "@/lib/api/autoscout/types/generated";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { MapPin, Phone, Mail, ExternalLink } from "lucide-react";
import Link from "next/link";
import { sanityFetch } from "@/sanity/lib/live";
import { centerByAutoscoutSellerIdQuery } from "@/sanity/lib/queries/documents/center";
import type { Center } from "../../../../../../sanity.types";

interface VehicleSellerInfoProps {
  vehicle: Schemas.ListingResponse;
}

export default async function VehicleSellerInfo({
  vehicle,
}: VehicleSellerInfoProps) {
  const sellerId = vehicle.sellerId;
  const { data: seller }: { data: Center } = await sanityFetch({
    query: centerByAutoscoutSellerIdQuery,
    params: {
      sellerId: sellerId?.toString(),
    },
    stega: false,
  });

  if (!seller) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Informations sur le vendeur</CardTitle>
          <CardDescription>
            Aucune information sur le vendeur disponible
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Check if we have a center ID that matches our internal centers
  const hasCenterPage = !!seller;
  const centerPageUrl = hasCenterPage ? `/centres/${seller.slug}` : undefined;

  return (
    <Card>
      <CardHeader>
        <CardTitle>{seller.name || "Vendeur"}</CardTitle>
        {seller.address && (
          <CardDescription className="flex items-center gap-1">
            <MapPin className="h-3.5 w-3.5" />
            <span>
              {seller.address.street}
              {seller.address.postalCode && `, ${seller.address.city}`}
            </span>
          </CardDescription>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Contact Information */}
        <div className="space-y-2">
          {seller.contact?.phone && (
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <a
                href={`tel:${seller.contact.phone}`}
                className="text-primary hover:underline"
              >
                {seller.contact.phone}
              </a>
            </div>
          )}

          {seller.contact?.email && (
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <a
                href={`mailto:${seller.contact.email}`}
                className="text-primary hover:underline"
              >
                {seller.contact.email}
              </a>
            </div>
          )}
        </div>

        {/* Additional Information */}
        {seller.shortDescription && (
          <div>
            <h4 className="font-medium mb-1">Présentation du centre</h4>
            <p className="text-sm text-muted-foreground">
              {seller.shortDescription}
            </p>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex flex-row flex-wrap appearance gap-4 max-w-xl ml-auto">
        <Button className="w-full lg:w-auto" asChild>
          <a href={`tel:${seller.contact?.phone || ""}`}>
            Contacter le vendeur
          </a>
        </Button>

        {hasCenterPage && (
          <Button variant="outline" className="w-full lg:w-auto" asChild>
            <Link href={centerPageUrl || "#"}>
              <ExternalLink className="h-4 w-4 mr-2" />
              Voir la page du concessionnaire
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
