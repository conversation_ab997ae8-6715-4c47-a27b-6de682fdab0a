import type { Metadata } from "next";
import { notFound } from "next/navigation";
import { processMetadata } from "@/lib/utils";
import { sanityFetch } from "@/sanity/lib/live";
import { PageBuilder } from "@/components/page-builder";
import { CenterFooter } from "@/components/centers/center-footer";
import { CenterInfo } from "@/components/centers/center-info";
import {
  centerBySlugQuery,
  centerSlugsQuery,
} from "@/sanity/lib/queries/documents/center";
import { CenterVehicles } from "@/components/centers/center-vehicles";
import type { CenterSlugsQueryResult } from "../../../../../sanity.types";

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateStaticParams() {
  const { data } = await sanityFetch({
    query: centerSlugsQuery,
    perspective: "published",
    stega: false,
  });
  return (data as CenterSlugsQueryResult).map((center) => ({
    slug: center.params.slug,
  }));
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { data: center } = await sanityFetch({
    query: centerBySlugQuery,
    params: await params,
    stega: false,
  });

  if (!center) {
    return {};
  }

  return processMetadata({ data: center });
}

export default async function CenterPage({ params }: PageProps) {
  const { data: center } = await sanityFetch({
    query: centerBySlugQuery,
    params: await params,
  });

  if (center === null) notFound();

  const hasPageBuilder = center.pageBuilder && center.pageBuilder.length > 0;

  return (
    <>
      <main className="pt-24">
        {/* Page Builder Content */}
        {hasPageBuilder && (
          <PageBuilder
            id={center._id}
            type="center"
            pageBuilder={center.pageBuilder}
          />
        )}

        {/* Dynamic content based on center information */}
        <section id="info" className="py-12">
          <div className="container mx-auto px-4">
            <CenterInfo center={center} />
          </div>
        </section>

        {/* Vehicle Listings */}
        <section id="vehicles" className="py-12">
          <div className="container mx-auto px-4">
            <CenterVehicles center={center} />
          </div>
        </section>
        {/* Center Footer */}
        <CenterFooter center={center} />
      </main>
    </>
  );
}
