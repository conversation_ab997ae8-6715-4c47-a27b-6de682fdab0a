import type { Metada<PERSON> } from "next";
import { sanityFetch } from "@/sanity/lib/live";
import { allCentersQuery } from "@/sanity/lib/queries/documents/center";
import { CenterCard } from "@/components/centers/center-card";
import { CenterTypeFilter } from "@/components/centers/center-type-filter";
import { CentersLayout } from "@/components/centers/centers-layout";
import type { Center } from "../../../../sanity.types";

export const metadata: Metadata = {
  title: "Nos Centres Autocorner",
  description:
    "Découvrez nos centres Audi, Skoda et véhicules d'occasion en Suisse. Trouvez le centre le plus proche de chez vous.",
};

interface PageProps {
  searchParams: Promise<{ type?: string }>;
}

export default async function CentersPage({ searchParams }: PageProps) {
  const { type } = await searchParams;

  const { data: centers }: { data: Center[] } = await sanityFetch({
    query: allCentersQuery,
  });

  if (!centers) return null;

  // Filter centers by type if specified
  const filteredCenters = type
    ? centers.filter((center) => center.centerType === type)
    : centers;

  // Group centers by type for display
  const centersByType = centers.reduce(
    (acc, center) => {
      const type = center.centerType || "other";
      if (!acc[type]) acc[type] = [];
      acc[type].push(center);
      return acc;
    },
    {} as Record<string, typeof centers>,
  );

  return (
    <CentersLayout>
      {/* Filter */}
      <CenterTypeFilter currentType={type} />

      {/* Centers Grid */}
      {type ? (
        // Show filtered results
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCenters.map((center) => (
            /* @ts-expect-error weak reference for images */
            <CenterCard key={center._id} center={center} />
          ))}
        </div>
      ) : (
        // Show grouped by type
        <div className="space-y-12">
          {Object.entries(centersByType).map(([centerType, typeCenters]) => (
            <section key={centerType}>
              <h2 className="text-2xl font-semibold mb-6 capitalize">
                {centerType === "audi" && "Centres Audi"}
                {centerType === "skoda" && "Centres Skoda"}
                {centerType === "occasions" && "Centres Occasions"}
                {!["audi", "skoda", "occasions"].includes(centerType) &&
                  "Autres Centres"}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {typeCenters.map((center) => (
                  /* @ts-expect-error weak reference for images */
                  <CenterCard key={center._id} center={center} />
                ))}
              </div>
            </section>
          ))}
        </div>
      )}

      {filteredCenters.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            Aucun centre trouvé pour ce type.
          </p>
        </div>
      )}
    </CentersLayout>
  );
}
