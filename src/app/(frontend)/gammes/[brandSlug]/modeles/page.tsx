import type { Metadata } from "next";
import { sanityFetch } from "@/sanity/lib/live";
import { vehicleModelsByBrandQuery } from "@/sanity/lib/queries/documents/vehicleModel";
import Heading from "@/components/shared/heading";
import { brandBySlugQuery } from "@/sanity/lib/queries/documents/brand";
import type {
  BrandBySlugQueryResult,
  VehicleModelsByBrandQueryResult,
} from "../../../../../../sanity.types";
import type { ModelsClientProps } from "./client";
import { ModelsClient } from "./client";
import type { InferPagePropsType } from "next-typesafe-url";
import type { RouteType } from "./routeType";
import BackButton from "@/components/shared/back-button";
import { stegaClean } from "next-sanity";

type PageProps = InferPagePropsType<RouteType>;

export const metadata: Metadata = {
  title: "Tous les véhicules - Autocorner",
  description:
    "Explorez notre gamme complète de véhicules de toutes les marques",
};

export default async function VehiclesPage({
  params,
  searchParams,
}: {
  params: Promise<{ brandSlug: string }>;
  searchParams: Promise<never>;
}) {
  const paramsData = await params;
  const searchParamsData = (await searchParams) as PageProps;
  const [brandResult, vehicleModelsResult] = await Promise.all([
    sanityFetch({
      query: brandBySlugQuery,
      params: {
        slug: paramsData.brandSlug,
      },
    }),
    sanityFetch({
      query: vehicleModelsByBrandQuery,
      params: {
        slug: paramsData.brandSlug,
      },
    }),
  ]);

  const { data: brand }: { data: BrandBySlugQueryResult } = brandResult;
  const { data: vehicleModels }: { data: VehicleModelsByBrandQueryResult } =
    vehicleModelsResult;

  return (
    <div
      className={"flex flex-col space-y-6 max-w-10xl mx-auto pt-8 lg:pt-0"}
      style={{
        "--brand-color-primary": stegaClean(
          brand?.brandColors?.primaryColor?.value,
        ),
        "--brand-color-secondary": stegaClean(
          brand?.brandColors?.secondaryColor?.value,
        ),
        "--brand-color-accent": stegaClean(
          brand?.brandColors?.accentColor?.value,
        ),
      }}
    >
      <div className="mb-12 space-y-4">
        <BackButton
          href={`/gammes/${brand?.slug}`}
          title={`Retour à ${brand?.name}`}
        />
        <Heading tag="h1" size="xxxl" className="mb-4">
          Modèles {brand?.name}
        </Heading>
        <p className="text-lg text-gray-600">
          Découvrez notre gamme complète de modèles de marques premium et
          familiales
        </p>
      </div>

      <ModelsClient
        vehicleModels={vehicleModels}
        searchParams={searchParamsData as ModelsClientProps["searchParams"]}
        brand={brand}
      />
    </div>
  );
}
