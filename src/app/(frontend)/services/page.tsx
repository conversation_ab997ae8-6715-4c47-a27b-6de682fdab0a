import type { Metadata } from "next";
import { processMetadata } from "@/lib/utils";
import { sanityFetch } from "@/sanity/lib/live";
import ServiceGrid from "./_components/service-grid";
import {
  servicesPageQuery,
  allServicesQuery,
} from "@/sanity/lib/queries/documents/service";

export async function generateMetadata(): Promise<Metadata> {
  const { data: page } = await sanityFetch({
    query: servicesPageQuery,
    stega: false,
  });

  if (!page) {
    return {};
  }

  return processMetadata({ data: page });
}

export default async function ServicesPage() {
  const { data: services } = await sanityFetch({
    query: allServicesQuery,
  });

  const { data: page } = await sanityFetch({
    query: servicesPageQuery,
  });

  return (
    <>
      <div>
        <div className="mb-10">
          {page?.title && (
            <p className="text-xl">Découvrez nos services spécialisés.</p>
          )}
        </div>

        <ServiceGrid services={services} />
      </div>
    </>
  );
}
