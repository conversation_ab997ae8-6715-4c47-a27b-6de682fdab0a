import type { Metadata } from "next";
import { processMetadata } from "@/lib/utils";
import { sanityFetch } from "@/sanity/lib/live";
import ServiceGrid from "./_components/service-grid";
import {
  servicesPageQuery,
  allServicesQuery,
} from "@/sanity/lib/queries/documents/service";

export async function generateMetadata(): Promise<Metadata> {
  const { data: page } = await sanityFetch({
    query: servicesPageQuery,
    stega: false,
  });

  if (!page) {
    return {};
  }

  return processMetadata({ data: page });
}

export default async function ServicesPage() {
  const [servicesResult] = await Promise.all([
    sanityFetch({
      query: allServicesQuery,
    }),
    sanityFetch({
      query: servicesPageQuery,
    }),
  ]);

  const { data: services } = servicesResult;

  return (
    <>
      <div>
        <ServiceGrid services={services} />
      </div>
    </>
  );
}
