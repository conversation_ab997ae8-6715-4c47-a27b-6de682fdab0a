"use client";
import type { ReactNode } from "react";
import { usePathname } from "next/navigation";
import Container from "@/components/global/container";
import { PageBuilder } from "@/components/page-builder";
import type { PageBuilderProps } from "@/components/page-builder";
import type { ServicesPageQueryResult } from "../../../../../sanity.types";

interface ServicesLayoutProps {
  children: ReactNode;
  page: ServicesPageQueryResult;
}

export default function ServicesLayout({
  children,
  page,
}: ServicesLayoutProps) {
  const pathname = usePathname();

  if (!page) {
    return <Container className="pt-28">{children}</Container>;
  }

  // Main services page layout
  if (pathname === "/services") {
    // Filter out form blocks from PageBuilder for main page
    const pageBuilderBlocks =
      page.pageBuilder?.filter((block) => block._type !== "formBlock") || [];
    const formBlocks =
      page.pageBuilder?.filter((block) => block._type === "formBlock") || [];

    return (
      <div>
        {/* PageBuilder content first (without form blocks) */}
        {pageBuilderBlocks.length > 0 && (
          <PageBuilder
            pageBuilder={pageBuilderBlocks as PageBuilderProps["pageBuilder"]}
            id={page._id}
            type={"servicesPage"}
            inGrid={false}
          />
        )}

        {/* Main services content */}
        <Container className={pageBuilderBlocks.length > 0 ? "py-16" : "pt-28"}>
          <div>
            {!pageBuilderBlocks?.find(
              (block) =>
                block._type === "heroBlock" ||
                block._type === "headerBlock" ||
                block._type === "callToActionBlock",
            ) && (
              <div className="flex flex-col gap-4">
                <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
                  {page.title}
                </h1>
                <p>
                  Chez Autocorner, nous prenons soin de votre véhicule à chaque
                  étape : entretien, réparations, diagnostics, pneus et
                  carrosserie, pour garantir sécurité, performance et
                  esthétique.
                </p>
              </div>
            )}
            <div className="mt-8">{children}</div>
          </div>
        </Container>

        {/* Form blocks at the bottom */}
        {formBlocks.length > 0 && (
          <PageBuilder
            pageBuilder={formBlocks as PageBuilderProps["pageBuilder"]}
            id={page._id}
            type={"servicesPage"}
            inGrid={false}
          />
        )}
      </div>
    );
  }

  // Individual service pages
  return <Container className="pt-28">{children}</Container>;
}
