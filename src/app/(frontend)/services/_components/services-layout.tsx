import type { ReactNode } from "react";
import Container from "@/components/global/container";
import { PageBuilder } from "@/components/page-builder";
import type { PageBuilderProps } from "@/components/page-builder";
import type { ServicesPageQueryResult } from "../../../../../sanity.types";

interface ServicesLayoutProps {
  children: ReactNode;
  page: ServicesPageQueryResult;
}

export default function ServicesLayout({ children, page }: ServicesLayoutProps) {
  if (!page) {
    return (
      <Container className="pt-28">
        {children}
      </Container>
    );
  }

  const hasPageBuilder = page.pageBuilder && page.pageBuilder.length > 0;

  return (
    <>
      {/* PageBuilder content first */}
      {hasPageBuilder && (
        <PageBuilder
          pageBuilder={page.pageBuilder as PageBuilderProps["pageBuilder"]}
          id={page._id}
          type={"servicesPage"}
          inGrid={false}
        />
      )}

      {/* Main services content */}
      <Container className={hasPageBuilder ? "py-16" : "pt-28"}>
        <div>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
            {page.title}
          </h1>
          <div className="mt-8">{children}</div>
        </div>
      </Container>
    </>
  );
}
