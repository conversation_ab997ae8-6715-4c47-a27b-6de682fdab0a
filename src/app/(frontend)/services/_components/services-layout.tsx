import type { ReactNode } from "react";
import Container from "@/components/global/container";
import { PageBuilder } from "@/components/page-builder";
import type { PageBuilderProps } from "@/components/page-builder";
import type { ServicesPageQueryResult } from "../../../../../sanity.types";

interface ServicesLayoutProps {
  children: ReactNode;
  page: ServicesPageQueryResult;
}

export default function ServicesLayout({ children, page }: ServicesLayoutProps) {
  if (!page) {
    return (
      <Container variant="large" className="pt-28">
        {children}
      </Container>
    );
  }

  return (
    <>
      <Container variant="large" className="pt-28">
        <div>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
            {page.title}
          </h1>
          <div className="mt-8">{children}</div>
        </div>
      </Container>
      {page.pageBuilder && (
        <PageBuilder
          pageBuilder={page.pageBuilder as PageBuilderProps["pageBuilder"]}
          id={page._id}
          type={"servicesPage"}
          inGrid={false}
        />
      )}
    </>
  );
}
