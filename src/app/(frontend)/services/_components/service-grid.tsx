"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import ServiceCard from "./service-card";
import type { AllServicesQueryResult } from "../../../../../sanity.types";
import { But<PERSON> } from "@/components/ui/button";
import { Filter, Building2, Users } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON>et<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

type Service = NonNullable<AllServicesQueryResult>[number];

interface ServiceFilters {
  serviceScope?: string;
  serviceCategory?: string;
}

interface ServiceGridProps {
  services: Service[];
}

export default function ServiceGrid({ services }: ServiceGridProps) {
  const searchParams = useSearchParams();
  const [filteredServices, setFilteredServices] = useState<Service[]>(services);
  const [filters, setFilters] = useState<ServiceFilters>({
    serviceScope: searchParams.get("scope") || undefined,
  });

  // Extract unique service categories from B2B services
  const serviceCategories = Array.from(
    new Set(
      services
        .filter((service) => service.serviceScope === "b2b" && service.b2b?.serviceCategory)
        .map((service) => service.b2b?.serviceCategory)
        .filter(Boolean)
    )
  );

  // Apply filters when they change
  useEffect(() => {
    let result = [...services];

    if (filters.serviceScope) {
      result = result.filter((service) => service.serviceScope === filters.serviceScope);
    }

    if (filters.serviceCategory) {
      result = result.filter(
        (service) => service.b2b?.serviceCategory === filters.serviceCategory
      );
    }

    setFilteredServices(result);
  }, [filters, services]);

  const handleFilterChange = (key: keyof ServiceFilters, value: string | undefined) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value === prev[key] ? undefined : value,
    }));
  };

  if (!services || services.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-2xl flex items-center justify-center">
            🛠️
          </div>
          <h3 className="text-lg font-semibold mb-2">Aucun service trouvé</h3>
          <p className="text-sm">Revenez plus tard pour de nouveaux services.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="grid grid-cols-12 gap-8">
        {/* Filters Sidebar - Desktop */}
        <div className="hidden lg:block col-span-12 lg:col-span-3 sticky top-24">
          <ServiceFilters
            serviceCategories={serviceCategories}
            filters={filters}
            onFilterChange={handleFilterChange}
          />
        </div>

        {/* Services Grid */}
        <div className="col-span-12 lg:col-span-9">
          {/* Applied filters badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            {filters.serviceScope && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {filters.serviceScope === "b2b" ? (
                  <Building2 size={12} />
                ) : (
                  <Users size={12} />
                )}
                {filters.serviceScope === "b2b" ? "Services B2B" : "Services Particuliers"}
              </Badge>
            )}
            {filters.serviceCategory && (
              <Badge variant="secondary">
                Catégorie : {translateServiceCategory(filters.serviceCategory)}
              </Badge>
            )}
          </div>

          {filteredServices.length > 0 ? (
            <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 items-stretch">
              {filteredServices.map((service, index) => (
                <ServiceCard
                  key={service._id}
                  service={service}
                  priority={index < 6 ? "high" : "normal"}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-accent rounded-lg">
              <h3 className="text-xl font-semibold">
                Aucun service ne correspond à vos filtres
              </h3>
              <p className="mt-2">
                Essayez d'ajuster vos filtres ou parcourez tous les services.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Filter Button */}
      <div className="block lg:hidden fixed bottom-0 left-0 right-0 w-full z-50">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="secondary" className="w-full h-14 rounded-none">
              <Filter className="h-4 w-4 mr-2" />
              Filtres{" "}
              {Object.values(filters).filter(Boolean).length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {Object.values(filters).filter(Boolean).length}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[80vh]">
            <SheetHeader className="flex items-center justify-between mb-6">
              <SheetTitle className="text-lg font-semibold">Filtres</SheetTitle>
            </SheetHeader>
            <ScrollArea className="h-full pb-8 p-4">
              <ServiceFilters
                serviceCategories={serviceCategories}
                filters={filters}
                onFilterChange={handleFilterChange}
              />
            </ScrollArea>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  );
}

interface ServiceFiltersProps {
  serviceCategories: (string | undefined)[];
  filters: ServiceFilters;
  onFilterChange: (key: keyof ServiceFilters, value: string | undefined) => void;
}

function ServiceFilters({ serviceCategories, filters, onFilterChange }: ServiceFiltersProps) {
  return (
    <div className="space-y-6">
      {/* Service Scope Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Type de service</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={filters.serviceScope || ""}
            onValueChange={(value) => onFilterChange("serviceScope", value || undefined)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="" id="scope-all" />
              <Label htmlFor="scope-all" className="text-sm">
                Tous les services
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="consumer" id="scope-consumer" />
              <Label htmlFor="scope-consumer" className="text-sm flex items-center gap-2">
                <Users size={14} />
                Particuliers
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="b2b" id="scope-b2b" />
              <Label htmlFor="scope-b2b" className="text-sm flex items-center gap-2">
                <Building2 size={14} />
                Entreprises (B2B)
              </Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Service Category Filter (only show if B2B is selected or no scope filter) */}
      {(!filters.serviceScope || filters.serviceScope === "b2b") && serviceCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Catégorie B2B</CardTitle>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={filters.serviceCategory || ""}
              onValueChange={(value) => onFilterChange("serviceCategory", value || undefined)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="" id="category-all" />
                <Label htmlFor="category-all" className="text-sm">
                  Toutes les catégories
                </Label>
              </div>
              {serviceCategories.map((category) => (
                <div key={category} className="flex items-center space-x-2">
                  <RadioGroupItem value={category || ""} id={`category-${category}`} />
                  <Label htmlFor={`category-${category}`} className="text-sm">
                    {translateServiceCategory(category || "")}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function translateServiceCategory(category: string): string {
  const translations: Record<string, string> = {
    sales: "Vente",
    financing: "Financement",
    maintenance: "Maintenance",
    digital: "Services Numériques",
    consultation: "Consultation",
  };
  return translations[category] || category;
}
