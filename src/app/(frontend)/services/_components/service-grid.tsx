"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import ServiceCard from "./service-card";
import type { AllServicesQueryResult } from "../../../../../sanity.types";
import { Button } from "@/components/ui/button";
import { Building2, Users } from "lucide-react";

type Service = NonNullable<AllServicesQueryResult>[number];

interface ServiceFilters {
  serviceScope?: string;
  serviceCategory?: string;
}

interface ServiceGridProps {
  services: Service[];
}

export default function ServiceGrid({ services }: ServiceGridProps) {
  const searchParams = useSearchParams();
  const [filteredServices, setFilteredServices] = useState<Service[]>(services);
  const [filters, setFilters] = useState<ServiceFilters>({
    serviceScope: searchParams.get("scope") || undefined,
  });

  // Extract unique service categories from B2B services
  const serviceCategories = Array.from(
    new Set(
      services
        .filter((service) => service.serviceScope === "b2b" && service.b2b?.serviceCategory)
        .map((service) => service.b2b?.serviceCategory)
        .filter(Boolean)
    )
  );

  // Apply filters when they change
  useEffect(() => {
    let result = [...services];

    if (filters.serviceScope) {
      result = result.filter((service) => service.serviceScope === filters.serviceScope);
    }

    if (filters.serviceCategory) {
      result = result.filter(
        (service) => service.b2b?.serviceCategory === filters.serviceCategory
      );
    }

    setFilteredServices(result);
  }, [filters, services]);

  const handleFilterChange = (key: keyof ServiceFilters, value: string | undefined) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value === prev[key] ? undefined : value,
    }));
  };

  if (!services || services.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-2xl flex items-center justify-center">
            🛠️
          </div>
          <h3 className="text-lg font-semibold mb-2">Aucun service trouvé</h3>
          <p className="text-sm">Revenez plus tard pour de nouveaux services.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Service Scope Filter - Button Group */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Type de service</h2>
          <p className="text-sm text-muted-foreground">
            Choisissez le type de service qui vous intéresse
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={!filters.serviceScope ? "primary" : "outline"}
            onClick={() => handleFilterChange("serviceScope", undefined)}
            className="flex items-center gap-2"
          >
            Tous les services
          </Button>
          <Button
            variant={filters.serviceScope === "consumer" ? "primary" : "outline"}
            onClick={() => handleFilterChange("serviceScope", "consumer")}
            className="flex items-center gap-2"
          >
            <Users size={16} />
            Particuliers
          </Button>
          <Button
            variant={filters.serviceScope === "b2b" ? "primary" : "outline"}
            onClick={() => handleFilterChange("serviceScope", "b2b")}
            className="flex items-center gap-2"
          >
            <Building2 size={16} />
            Entreprises
          </Button>
        </div>
      </div>

      {/* B2B Category Filter - Only show when B2B is selected */}
      {filters.serviceScope === "b2b" && serviceCategories.length > 0 && (
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div>
            <h3 className="text-base font-medium">Catégorie B2B</h3>
            <p className="text-sm text-muted-foreground">
              Filtrer par catégorie de service professionnel
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={!filters.serviceCategory ? "primary" : "outline"}
              onClick={() => handleFilterChange("serviceCategory", undefined)}
              size="sm"
            >
              Toutes
            </Button>
            {serviceCategories.map((category) => (
              <Button
                key={category}
                variant={filters.serviceCategory === category ? "primary" : "outline"}
                onClick={() => handleFilterChange("serviceCategory", category || undefined)}
                size="sm"
              >
                {translateServiceCategory(category || "")}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Services Grid */}
      {filteredServices.length > 0 ? (
        <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-6 items-stretch">
          {filteredServices.map((service, index) => (
            <ServiceCard
              key={service.slug || index}
              service={service}
              priority={index < 6 ? "high" : "normal"}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-muted/50 rounded-lg">
          <h3 className="text-xl font-semibold mb-2">
            Aucun service ne correspond à vos critères
          </h3>
          <p className="text-muted-foreground">
            Essayez de modifier vos filtres ou parcourez tous les services.
          </p>
        </div>
      )}
    </div>
  );
}

function translateServiceCategory(category: string): string {
  const translations: Record<string, string> = {
    sales: "Vente",
    financing: "Financement",
    maintenance: "Maintenance",
    digital: "Services Numériques",
    consultation: "Consultation",
  };
  return translations[category] || category;
}
