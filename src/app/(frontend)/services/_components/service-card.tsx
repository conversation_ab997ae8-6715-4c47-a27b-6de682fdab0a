import React from "react";
import Link from "next/link";
import Image from "next/image";
import { ChevronRight, BriefcaseBusiness, Building2, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Heading from "@/components/shared/heading";
import AnimatedUnderline from "@/components/shared/animated-underline";
import type { AllServicesQueryResult } from "../../../../../sanity.types";
import { cn } from "@/lib/utils";

interface ServiceCardProps {
  service: AllServicesQueryResult[number];
  variant?: "standard" | "compact";
  priority?: "high" | "normal";
}

export default function ServiceCard({
  service,
  variant = "standard",
  priority = "normal",
}: ServiceCardProps) {
  const {
    title,
    slug,
    shortDescription,
    image,
    serviceScope,
    b2b,
  } = service;

  const isB2B = serviceScope === "b2b";

  return (
    <article
      aria-label={title ?? ""}
      className="relative group pb-8 border-b border-dashed h-full flex flex-col"
    >
      <Link href={`/services/${slug}`} className="relative flex-1 flex flex-col">
        {/* Service Scope Badge */}
        <div className="z-10 absolute top-6 left-6 px-2 py-1 rounded-md text-xs font-medium bg-background/80 backdrop-blur-sm border">
          <div className="flex items-center gap-1">
            {isB2B ? (
              <Building2 size={12} />
            ) : (
              <Users size={12} />
            )}
            {isB2B ? "B2B" : "Particuliers"}
          </div>
        </div>

        {/* Service Visual */}
        <ServiceThumbnail
          image={image}
          title={title}
          serviceScope={serviceScope}
          priority={priority === "high"}
        />

        {/* Content */}
        <div className="mt-5 md:mt-6 flex flex-col flex-1">
          {/* Top content that can grow */}
          <div className="space-y-4 flex-1">
            {/* Service Title */}
            <Heading tag="h2" size="md" className="text-balance">
              {title}
            </Heading>

            {/* B2B Category */}
            {variant === "standard" && isB2B && b2b?.serviceCategory && (
              <div className="flex flex-wrap gap-1">
                <Badge variant="secondary" className="text-xs">
                  {translateServiceCategory(b2b.serviceCategory)}
                </Badge>
              </div>
            )}

            {/* Description */}
            {variant === "standard" && shortDescription && (
              <p className="text-balance text-muted-foreground text-sm line-clamp-3">
                {shortDescription}
              </p>
            )}

            {/* B2B Benefits Preview */}
            {variant === "standard" && isB2B && b2b?.businessBenefits && (
              <p className="text-balance text-muted-foreground text-xs italic line-clamp-2">
                {b2b.businessBenefits}
              </p>
            )}
          </div>

          {/* Footer - Sticks to bottom */}
          {variant === "standard" && (
            <div className="flex items-center justify-between pt-4 mt-auto">
              <div className="text-sm text-muted-foreground">
                En savoir plus
              </div>
              <ChevronRight
                size={18}
                className="-translate-x-6 opacity-0 group-hover:-translate-x-0 group-hover:opacity-100 transition-all duration-300 text-muted-foreground"
              />
            </div>
          )}
        </div>
      </Link>
      <AnimatedUnderline className="-translate-y-0.5" />
    </article>
  );
}

function ServiceThumbnail({
  image,
  title,
  serviceScope,
  priority = false,
}: {
  image?: {
    asset?: { url?: string | null } | null;
    altText?: string | null;
  } | null;
  title?: string | null;
  serviceScope?: string | null;
  priority?: boolean;
}) {
  const isB2B = serviceScope === "b2b";

  return (
    <div className="h-72 p-4 rounded-3xl border border-dashed backdrop-blur-md backdrop-opacity-50 pattern-bg transition-all duration-200 ease-out group-hover:border-primary">
      <div className="h-full rounded-2xl flex items-center justify-center relative overflow-hidden bg-muted/50">
        {image?.asset?.url ? (
          <div className="flex relative items-center justify-center w-[80%] h-[80%]">
            <Image
              src={image.asset.url}
              priority={priority}
              width={200}
              height={150}
              alt={image.altText ?? `${title} service image`}
              className="aspect-auto rounded-2xl object-cover"
            />
          </div>
        ) : (
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center text-primary bg-primary/10 border border-dashed">
              <BriefcaseBusiness size={32} />
            </div>
            <div className="text-lg font-semibold text-muted-foreground">
              {title}
            </div>
          </div>
        )}

        {/* Service scope accent */}
        <div className="absolute bottom-0 left-0 w-full h-1 rounded-b-2xl bg-primary/20" />
      </div>
    </div>
  );
}

function translateServiceCategory(category: string): string {
  const translations: Record<string, string> = {
    sales: "Vente",
    financing: "Financement",
    maintenance: "Maintenance",
    digital: "Services Numériques",
    consultation: "Consultation",
  };
  return translations[category] || category;
}
