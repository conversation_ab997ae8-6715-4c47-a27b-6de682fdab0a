import type { Metadata } from "next";
import { notFound } from "next/navigation";
import { processMetadata } from "@/lib/utils";
import { sanityFetch } from "@/sanity/lib/live";
import { PageBuilder } from "@/components/page-builder";
import ServiceHero from "./_components/service-hero";
import RelatedServices from "./_components/related-services";
import ServiceConnections from "./_components/service-connections";
import {
  serviceBySlugQuery,
  serviceSlugsQuery,
  relatedServicesQuery,
} from "@/sanity/lib/queries/documents/service";

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateStaticParams() {
  const { data } = await sanityFetch({
    query: serviceSlugsQuery,
    perspective: "published",
    stega: false,
  });
  return data;
}

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { data: service } = await sanityFetch({
    query: serviceBySlugQuery,
    params: await params,
    stega: false,
  });

  if (!service) {
    return {};
  }

  return processMetadata({ data: service });
}

export default async function ServicePage({ params }: PageProps) {
  const paramsData = await params;

  const [serviceResult] = await Promise.all([
    sanityFetch({
      query: serviceBySlugQuery,
      params: paramsData,
    }),
    sanityFetch({
      query: relatedServicesQuery,
      params: {
        serviceScope: "", // Will be filled after we get the service
        currentServiceId: "",
      },
    }),
  ]);

  const { data: service } = serviceResult;

  if (service === null) notFound();

  // Fetch related services with the correct parameters
  const { data: relatedServices } = await sanityFetch({
    query: relatedServicesQuery,
    params: {
      serviceScope: service.serviceScope || "consumer",
      currentServiceId: service._id,
    },
  });

  return (
    <div>
      {/* Service Hero with auto-generated content */}
      <ServiceHero service={service} />

      {/* Custom PageBuilder content */}
      {service?.pageBuilder && service.pageBuilder.length > 0 && (
        <PageBuilder
          id={service._id}
          type="service"
          pageBuilder={service.pageBuilder}
        />
      )}

      {/* Related Services */}
      <RelatedServices
        services={relatedServices}
        currentServiceScope={service.serviceScope}
      />

      {/* Service Connections */}
      <ServiceConnections
        serviceScope={service.serviceScope}
        serviceTitle={service.title}
      />
    </div>
  );
}
