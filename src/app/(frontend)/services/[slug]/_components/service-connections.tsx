import React from "react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Gift, 
  MapPin, 
  Car, 
  ChevronRight,
  Building2,
  Users
} from "lucide-react";
import Container from "@/components/global/container";
import Heading from "@/components/shared/heading";

interface ServiceConnectionsProps {
  serviceScope?: string;
  serviceTitle?: string;
}

export default function ServiceConnections({ serviceScope, serviceTitle }: ServiceConnectionsProps) {
  const isB2B = serviceScope === "b2b";

  return (
    <Container className="py-16 bg-accent/30">
      <div className="text-center mb-12">
        <Heading tag="h2" size="lg">
          Découvrez nos autres services
        </Heading>
        <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
          Explorez notre gamme complète de services, offres spéciales et centres spécialisés
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Related Offers */}
        <Card className="group hover:shadow-lg transition-all duration-300">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Gift className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <CardTitle className="text-lg">Offres spéciales</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Découvrez nos offres promotionnelles et avantages exclusifs pour compléter votre service.
            </p>
            <Button variant="outline" asChild className="w-full group-hover:bg-green-50 dark:group-hover:bg-green-900/10">
              <Link href="/offres" className="flex items-center justify-between">
                Voir les offres
                <ChevronRight size={16} />
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Related Centers */}
        <Card className="group hover:shadow-lg transition-all duration-300">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <MapPin className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle className="text-lg">Nos centres</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Trouvez le centre AutoCorner le plus proche pour bénéficier de nos services.
            </p>
            <Button variant="outline" asChild className="w-full group-hover:bg-blue-50 dark:group-hover:bg-blue-900/10">
              <Link href="/centres" className="flex items-center justify-between">
                Localiser un centre
                <ChevronRight size={16} />
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Related Brands/Vehicles */}
        <Card className="group hover:shadow-lg transition-all duration-300">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <Car className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <CardTitle className="text-lg">Nos gammes</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground text-sm">
              Explorez notre sélection de véhicules et marques partenaires.
            </p>
            <Button variant="outline" asChild className="w-full group-hover:bg-purple-50 dark:group-hover:bg-purple-900/10">
              <Link href="/gammes" className="flex items-center justify-between">
                Découvrir les gammes
                <ChevronRight size={16} />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Service Scope Specific Links */}
      <div className="mt-12 p-6 bg-background rounded-lg border">
        <div className="flex items-center gap-3 mb-4">
          {isB2B ? (
            <Building2 className="h-5 w-5 text-blue-600" />
          ) : (
            <Users className="h-5 w-5 text-green-600" />
          )}
          <h3 className="font-semibold">
            {isB2B ? "Services B2B complémentaires" : "Services particuliers"}
          </h3>
        </div>
        <p className="text-muted-foreground text-sm mb-4">
          {isB2B 
            ? "Découvrez notre gamme complète de solutions professionnelles adaptées aux entreprises."
            : "Explorez tous nos services dédiés aux particuliers pour une expérience complète."
          }
        </p>
        <Button variant="secondary" asChild>
          <Link href={`/services?scope=${serviceScope || 'consumer'}`} className="flex items-center gap-2">
            Voir tous les services {isB2B ? "B2B" : "particuliers"}
            <ChevronRight size={16} />
          </Link>
        </Button>
      </div>
    </Container>
  );
}
