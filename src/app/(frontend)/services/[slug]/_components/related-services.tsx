import React from "react";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import ServiceCard from "../../_components/service-card";
import Container from "@/components/global/container";
import Heading from "@/components/shared/heading";
import type { AllServicesQueryResult } from "../../../../../../sanity.types";

interface RelatedServicesProps {
  services: AllServicesQueryResult;
  currentServiceScope?: string;
}

export default function RelatedServices({ services, currentServiceScope }: RelatedServicesProps) {
  if (!services || services.length === 0) {
    return null;
  }

  const scopeLabel = currentServiceScope === "b2b" ? "B2B" : "Particuliers";

  return (
    <Container className="py-16">
      <div className="flex items-center justify-between mb-8">
        <div>
          <Heading tag="h2" size="lg">
            Autres services {scopeLabel}
          </Heading>
          <p className="text-muted-foreground mt-2">
            Découvrez nos autres services dans la même catégorie
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/services" className="flex items-center gap-2">
            Voir tous les services
            <ChevronRight size={16} />
          </Link>
        </Button>
      </div>

      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
        {services.map((service, index) => (
          <ServiceCard
            key={service.slug || index}
            service={service}
            variant="compact"
            priority={index < 3 ? "high" : "normal"}
          />
        ))}
      </div>
    </Container>
  );
}
