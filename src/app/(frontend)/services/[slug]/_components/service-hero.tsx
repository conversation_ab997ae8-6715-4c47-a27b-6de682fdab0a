import React from "react";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  BriefcaseBusiness, 
  Building2, 
  Users, 
  Clock, 
  MapPin,
  DollarSign,
  Zap
} from "lucide-react";
import type { ServiceBySlugQueryResult } from "../../../../../../sanity.types";
import Container from "@/components/global/container";

interface ServiceHeroProps {
  service: ServiceBySlugQueryResult;
}

export default function ServiceHero({ service }: ServiceHeroProps) {
  const {
    title,
    shortDescription,
    image,
    serviceScope,
    b2b,
  } = service;

  const isB2B = serviceScope === "b2b";

  return (
    <Container className="py-12">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        {/* Content */}
        <div className="space-y-6">
          {/* Service Scope Badge */}
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              {isB2B ? (
                <Building2 size={14} />
              ) : (
                <Users size={14} />
              )}
              {isB2B ? "Service B2B" : "Service Particuliers"}
            </Badge>
            {isB2B && b2b?.serviceCategory && (
              <Badge variant="secondary">
                {translateServiceCategory(b2b.serviceCategory)}
              </Badge>
            )}
          </div>

          {/* Title */}
          <h1 className="text-4xl lg:text-5xl font-bold tracking-tight">
            {title}
          </h1>

          {/* Description */}
          {shortDescription && (
            <p className="text-xl text-muted-foreground leading-relaxed">
              {shortDescription}
            </p>
          )}

          {/* B2B Business Benefits */}
          {isB2B && b2b?.businessBenefits && (
            <div className="p-4 bg-muted/50 rounded-lg border">
              <h3 className="font-semibold mb-2">
                Avantages pour votre entreprise
              </h3>
              <p className="text-muted-foreground text-sm">
                {b2b.businessBenefits}
              </p>
            </div>
          )}

          {/* B2B Quick Info */}
          {isB2B && b2b && (
            <div className="grid grid-cols-2 gap-4">
              {b2b.responseTime && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock size={16} className="text-muted-foreground" />
                  <span className="text-muted-foreground">Délai de réponse:</span>
                  <span className="font-medium">{b2b.responseTime}</span>
                </div>
              )}
              {b2b.deliveryMethod && (
                <div className="flex items-center gap-2 text-sm">
                  <MapPin size={16} className="text-muted-foreground" />
                  <span className="text-muted-foreground">Livraison:</span>
                  <span className="font-medium">{translateDeliveryMethod(b2b.deliveryMethod)}</span>
                </div>
              )}
              {b2b.pricingModel && (
                <div className="flex items-center gap-2 text-sm">
                  <DollarSign size={16} className="text-muted-foreground" />
                  <span className="text-muted-foreground">Tarification:</span>
                  <span className="font-medium">{translatePricingModel(b2b.pricingModel)}</span>
                </div>
              )}
              {b2b.implementationTime && (
                <div className="flex items-center gap-2 text-sm">
                  <Zap size={16} className="text-muted-foreground" />
                  <span className="text-muted-foreground">Mise en œuvre:</span>
                  <span className="font-medium">{b2b.implementationTime}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Image */}
        <div className="relative">
          {image?.asset?.url ? (
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden">
              <Image
                src={image.asset.url}
                alt={image.altText ?? `${title} service`}
                fill
                className="object-cover"
                priority
              />
              {/* Service scope accent */}
              <div className="absolute bottom-0 left-0 w-full h-2 bg-primary/30" />
            </div>
          ) : (
            <Card className="aspect-[4/3] flex items-center justify-center">
              <CardContent className="text-center">
                <div className="w-20 h-20 mx-auto mb-4 rounded-2xl flex items-center justify-center text-primary bg-primary/10 border border-dashed">
                  <BriefcaseBusiness size={40} />
                </div>
                <h3 className="text-lg font-semibold text-muted-foreground">
                  {title}
                </h3>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* B2B Target Segments */}
      {isB2B && b2b?.targetSegments && b2b.targetSegments.length > 0 && (
        <div className="mt-12 p-6 bg-muted/30 rounded-lg">
          <h3 className="font-semibold mb-4">Segments cibles</h3>
          <div className="flex flex-wrap gap-2">
            {b2b.targetSegments.map((segment: string) => (
              <Badge key={segment} variant="secondary">
                {translateTargetSegment(segment)}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </Container>
  );
}

function translateServiceCategory(category: string): string {
  const translations: Record<string, string> = {
    sales: "Vente",
    financing: "Financement",
    maintenance: "Maintenance",
    digital: "Services Numériques",
    consultation: "Consultation",
  };
  return translations[category] || category;
}

function translateDeliveryMethod(method: string): string {
  const translations: Record<string, string> = {
    on_site: "Sur site",
    remote: "À distance",
    center: "En centre",
    hybrid: "Hybride",
  };
  return translations[method] || method;
}

function translatePricingModel(model: string): string {
  const translations: Record<string, string> = {
    included: "Inclus",
    additional: "Supplément",
    contact_required: "Sur devis",
  };
  return translations[model] || model;
}

function translateTargetSegment(segment: string): string {
  const translations: Record<string, string> = {
    sme: "PME",
    enterprise: "Grandes entreprises",
    fleet: "Gestionnaires de flotte",
    professionals: "Professionnels",
    startups: "Startups",
  };
  return translations[segment] || segment;
}
