import type { ReactNode } from "react";
import { sanityFetch } from "@/sanity/lib/live";
import ServicesLayout from "./_components/services-layout";
import { servicesPageQuery } from "@/sanity/lib/queries/documents/service";

export default async function ServicesArchiveLayout({
  children,
}: {
  children: ReactNode;
}) {
  const { data: page } = await sanityFetch({
    query: servicesPageQuery,
  });

  return <ServicesLayout page={page}>{children}</ServicesLayout>;
}
